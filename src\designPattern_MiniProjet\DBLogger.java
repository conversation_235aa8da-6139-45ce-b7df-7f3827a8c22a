package designPattern_MiniProjet;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class DBLogger implements LoggerStrategy {
	   public void log(String message) {
	        String url = "*********************************";
	        String user = "root";
	        String password = "";

	        String sql = "INSERT INTO logs (message) VALUES (?)";

	        try (Connection conn = DriverManager.getConnection(url, user, password);
	             PreparedStatement stmt = conn.prepareStatement(sql)) {

	            stmt.setString(1, message);
	            stmt.executeUpdate();
	            System.out.println("Log inséré en base de données.");

	        } catch (SQLException e) {
	            System.err.println("Erreur lors de l'insertion en base : " + e.getMessage());
	        }
	    }

}
