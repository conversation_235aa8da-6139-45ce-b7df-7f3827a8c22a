package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe concrète Rectangle
 * Version corrigée utilisant le pattern Strategy
 */
public class Rectangle extends Shape {
    
    public Rectangle(double x, double y, double width, double height, Color color) {
        super(x, y, width, height, color);
        this.drawingStrategy = new RectangleDrawingStrategy();
    }
    
    public Rectangle(double x, double y, double width, double height) {
        this(x, y, width, height, Color.BLUE);
    }
    
    public Rectangle(double x, double y) {
        this(x, y, 80, 60); // Dimensions par défaut
    }
    
    @Override
    public boolean contains(double pointX, double pointY) {
        return pointX >= x && pointX <= x + width && 
               pointY >= y && pointY <= y + height;
    }
    
    @Override
    public String serialize() {
        return String.format("RECTANGLE,%.2f,%.2f,%.2f,%.2f,%s", x, y, width, height, color.toString());
    }
    
    @Override
    public String getType() {
        return "Rectangle";
    }
    
    // Factory method pour créer depuis une chaîne sérialisée
    public static Rectangle deserialize(String data) {
        String[] parts = data.split(",");
        if (parts.length >= 5 && "RECTANGLE".equals(parts[0])) {
            double x = Double.parseDouble(parts[1]);
            double y = Double.parseDouble(parts[2]);
            double width = Double.parseDouble(parts[3]);
            double height = Double.parseDouble(parts[4]);
            Color color = parts.length > 5 ? Color.valueOf(parts[5]) : Color.BLUE;
            return new Rectangle(x, y, width, height, color);
        }
        throw new IllegalArgumentException("Invalid rectangle data: " + data);
    }
}
