package designPattern_MiniProjet;

import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Adapter pour la persistance des formes
 * Version corrigée qui utilise Shape au lieu de ShapeAdapter
 */
public class ShapePersistenceAdapter {
    private PersistenceStrategy strategy;
    
    public ShapePersistenceAdapter() {
        // Par défaut, utilise la persistance en fichier
        this.strategy = new FilePersistenceStrategy();
    }
    
    public ShapePersistenceAdapter(PersistenceStrategy strategy) {
        this.strategy = strategy;
    }
    
    public void setStrategy(PersistenceStrategy strategy) {
        this.strategy = strategy;
    }
    
    public void saveShapes(List<Shape> shapes, String destination) {
        try {
            strategy.save(shapes, destination);
            LoggerSingleton.getInstance().log("Formes sauvegardées avec succès vers: " + destination);
        } catch (Exception e) {
            LoggerSingleton.getInstance().log("Erreur lors de la sauvegarde: " + e.getMessage());
        }
    }
    
    public List<Shape> loadShapes(String source) {
        try {
            List<Shape> shapes = strategy.load(source);
            LoggerSingleton.getInstance().log("Formes chargées avec succès depuis: " + source);
            return shapes;
        } catch (Exception e) {
            LoggerSingleton.getInstance().log("Erreur lors du chargement: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}

/**
 * Interface Strategy pour la persistance - Version corrigée
 */
interface PersistenceStrategy {
    void save(List<Shape> shapes, String destination) throws Exception;
    List<Shape> load(String source) throws Exception;
}

/**
 * Strategy concrète pour la persistance en fichier - Version corrigée
 */
class FilePersistenceStrategy implements PersistenceStrategy {
    
    @Override
    public void save(List<Shape> shapes, String destination) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(destination))) {
            for (Shape shape : shapes) {
                writer.println(shape.serialize());
            }
        }
    }
    
    @Override
    public List<Shape> load(String source) throws IOException {
        List<Shape> shapes = new ArrayList<>();
        File file = new File(source);
        
        if (!file.exists()) {
            return shapes;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                try {
                    Shape shape = Shape.deserialize(line);
                    if (shape != null) {
                        shapes.add(shape);
                    }
                } catch (Exception e) {
                    System.err.println("Erreur lors de la désérialisation: " + line);
                }
            }
        }
        
        return shapes;
    }
}

/**
 * Strategy concrète pour la persistance en base de données - Version corrigée
 */
class DatabasePersistenceStrategy implements PersistenceStrategy {
    private static final String DB_URL = "**************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "";
    
    @Override
    public void save(List<Shape> shapes, String destination) throws SQLException {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            // Créer la table si elle n'existe pas
            createTableIfNotExists(conn);
            
            // Supprimer les anciennes données
            try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM shapes WHERE session_name = ?")) {
                stmt.setString(1, destination);
                stmt.executeUpdate();
            }
            
            // Insérer les nouvelles formes
            try (PreparedStatement stmt = conn.prepareStatement(
                    "INSERT INTO shapes (session_name, shape_data) VALUES (?, ?)")) {
                for (Shape shape : shapes) {
                    stmt.setString(1, destination);
                    stmt.setString(2, shape.serialize());
                    stmt.executeUpdate();
                }
            }
        }
    }
    
    @Override
    public List<Shape> load(String source) throws SQLException {
        List<Shape> shapes = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            createTableIfNotExists(conn);
            
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT shape_data FROM shapes WHERE session_name = ?")) {
                stmt.setString(1, source);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        try {
                            Shape shape = Shape.deserialize(rs.getString("shape_data"));
                            if (shape != null) {
                                shapes.add(shape);
                            }
                        } catch (Exception e) {
                            System.err.println("Erreur lors de la désérialisation depuis DB: " + e.getMessage());
                        }
                    }
                }
            }
        }
        
        return shapes;
    }
    
    private void createTableIfNotExists(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(
                "CREATE TABLE IF NOT EXISTS shapes (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "session_name VARCHAR(255), " +
                "shape_data TEXT)")) {
            stmt.executeUpdate();
        }
    }
}
