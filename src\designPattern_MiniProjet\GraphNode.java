package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe représentant un nœud de graphe pour l'application de graphe
 * Version corrigée - utilise la composition au lieu de l'héritage
 * @deprecated Utiliser designPattern_MiniProjet.graph.GraphNode à la place
 */
@Deprecated
public class GraphNode {
    private String label;
    private double x;
    private double y;
    private double radius;
    private Color color;
    private boolean isSelected = false;
    private boolean isHighlighted = false;
    
    public GraphNode(double x, double y, String label) {
        this.x = x;
        this.y = y;
        this.label = label;
        this.radius = 25;
        this.color = Color.LIGHTBLUE;
    }
    
    public GraphNode(double x, double y) {
        this(x, y, "");
    }
    
    public void draw(GraphicsContext gc) {
        // Couleur selon l'état
        Color fillColor;
        if (isHighlighted) {
            fillColor = Color.YELLOW;
        } else if (isSelected) {
            fillColor = Color.LIGHTGREEN;
        } else {
            fillColor = color;
        }
        
        // Dessiner le nœud
        gc.setFill(fillColor);
        gc.fillOval(x - radius, y - radius, radius * 2, radius * 2);
        
        // Contour
        gc.setStroke(Color.BLACK);
        gc.setLineWidth(2);
        gc.strokeOval(x - radius, y - radius, radius * 2, radius * 2);
        
        // Label
        if (label != null && !label.isEmpty()) {
            gc.setFill(Color.BLACK);
            gc.fillText(label, x - radius/2, y + radius/4);
        }
    }
    
    public boolean contains(double pointX, double pointY) {
        return Math.pow(pointX - x, 2) + Math.pow(pointY - y, 2) <= Math.pow(radius, 2);
    }
    
    // Getters and setters
    public String getLabel() { return label; }
    public void setLabel(String label) { this.label = label; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getRadius() { return radius; }
    public void setRadius(double radius) { this.radius = radius; }
    
    public Color getColor() { return color; }
    public void setColor(Color color) { this.color = color; }
    
    public boolean isSelected() { return isSelected; }
    public void setSelected(boolean selected) { this.isSelected = selected; }
    
    public boolean isHighlighted() { return isHighlighted; }
    public void setHighlighted(boolean highlighted) { this.isHighlighted = highlighted; }
}
