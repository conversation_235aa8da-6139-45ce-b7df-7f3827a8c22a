package designPattern_MiniProjet;

import designPattern_MiniProjet.graph.GraphNode;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.util.List;

/**
 * Application spécialisée pour la création et l'analyse de graphes
 * Étude de cas pour les algorithmes de plus court chemin
 */
public class GraphApplication extends Application {

    private Canvas canvas;
    private GraphicsContext gc;
    private Graph graph;
    private GraphNode selectedStartNode = null;
    private GraphNode selectedEndNode = null;
    private boolean nodeCreationMode = true;
    private boolean edgeCreationMode = false;
    private GraphNode tempNode = null; // Pour la création d'arêtes
    private int nodeCounter = 1;

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Application Graphe - Algorithmes de Plus Court Chemin");

        // Initialiser le logger
        LoggerSingleton.getInstance().setStrategy(new ConsoleLogger());

        // Initialiser le graphe
        graph = new Graph();

        // Interface utilisateur
        VBox root = createUI();

        Scene scene = new Scene(root, 1200, 800);
        primaryStage.setScene(scene);
        primaryStage.show();

        LoggerSingleton.getInstance().log("Application Graphe démarrée");
    }

    private VBox createUI() {
        VBox root = new VBox(10);
        root.setPadding(new Insets(10));

        // Barre d'outils pour les modes
        HBox modeToolbar = createModeToolbar();

        // Barre d'outils pour les algorithmes
        HBox algorithmToolbar = createAlgorithmToolbar();

        // Barre d'outils pour les actions
        HBox actionToolbar = createActionToolbar();

        // Zone d'information dynamique
        Label infoLabel = new Label("Mode: Creation de noeuds - Cliquez pour creer des noeuds");
        infoLabel.setStyle(
                "-fx-font-size: 14px; -fx-padding: 5px; -fx-background-color: #f0f0f0; -fx-border-color: #ccc;");

        // Zone d'état des sélections
        Label statusLabel = new Label("Noeud debut: Aucun | Noeud fin: Aucun");
        statusLabel.setStyle("-fx-font-size: 12px; -fx-padding: 5px; -fx-background-color: #e8f4f8;");

        // Zone de dessin
        canvas = new Canvas(1150, 500);
        gc = canvas.getGraphicsContext2D();
        gc.setFill(javafx.scene.paint.Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());

        // Gestion des clics sur le canvas
        canvas.setOnMouseClicked(e -> {
            if (nodeCreationMode) {
                createNode(e.getX(), e.getY());
                infoLabel.setText("Noeud cree a (" + String.format("%.0f", e.getX()) +
                        ", " + String.format("%.0f", e.getY()) + ")");
            } else if (edgeCreationMode) {
                handleEdgeCreation(e.getX(), e.getY(), infoLabel);
            } else {
                selectNode(e.getX(), e.getY(), infoLabel);
            }
            updateStatusLabel(statusLabel);
            redrawGraph();
        });

        root.getChildren().addAll(modeToolbar, algorithmToolbar, actionToolbar, infoLabel, statusLabel, canvas);
        return root;
    }

    private HBox createModeToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #f0f0f0;");

        Label modeLabel = new Label("Mode:");
        modeLabel.setStyle("-fx-font-weight: bold;");

        ToggleGroup modeGroup = new ToggleGroup();

        RadioButton nodeMode = new RadioButton("Creer noeuds");
        nodeMode.setToggleGroup(modeGroup);
        nodeMode.setSelected(true);
        nodeMode.setOnAction(e -> {
            nodeCreationMode = true;
            edgeCreationMode = false;
            tempNode = null;
            clearSelections();
        });

        RadioButton edgeMode = new RadioButton("Creer aretes");
        edgeMode.setToggleGroup(modeGroup);
        edgeMode.setOnAction(e -> {
            nodeCreationMode = false;
            edgeCreationMode = true;
            tempNode = null;
            clearSelections();
        });

        RadioButton selectMode = new RadioButton("Selectionner");
        selectMode.setToggleGroup(modeGroup);
        selectMode.setOnAction(e -> {
            nodeCreationMode = false;
            edgeCreationMode = false;
            tempNode = null;
        });

        toolbar.getChildren().addAll(modeLabel, nodeMode, edgeMode, selectMode);
        return toolbar;
    }

    private HBox createAlgorithmToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #e0e0e0;");

        Label algoLabel = new Label("Algorithme:");
        algoLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> algoComboBox = new ComboBox<>();
        algoComboBox.getItems().addAll("Dijkstra", "Bellman-Ford");
        algoComboBox.setValue("Dijkstra");

        algoComboBox.setOnAction(e -> {
            String selected = algoComboBox.getValue();
            switch (selected) {
                case "Dijkstra":
                    graph.setPathStrategy(new DijkstraStrategy());
                    break;
                case "Bellman-Ford":
                    graph.setPathStrategy(new BellmanFordStrategy());
                    break;
            }
            LoggerSingleton.getInstance().log("Algorithme changé: " + selected);
        });

        Button calculateButton = new Button("Calculer plus court chemin");
        calculateButton.setOnAction(e -> calculateShortestPath());

        Button clearPathButton = new Button("Effacer chemin");
        clearPathButton.setOnAction(e -> {
            graph.clearHighlights();
            redrawGraph();
            LoggerSingleton.getInstance().log("Chemin efface");
        });

        toolbar.getChildren().addAll(algoLabel, algoComboBox, calculateButton, clearPathButton);
        return toolbar;
    }

    private HBox createActionToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #d0d0d0;");

        Label actionLabel = new Label("Actions:");
        actionLabel.setStyle("-fx-font-weight: bold;");

        Button clearButton = new Button("Effacer tout");
        clearButton.setOnAction(e -> {
            graph.clear();
            nodeCounter = 1;
            clearSelections();
            redrawGraph();
            LoggerSingleton.getInstance().log("Graphe efface");
        });

        Button infoButton = new Button("Info graphe");
        infoButton.setOnAction(e -> showGraphInfo());

        toolbar.getChildren().addAll(actionLabel, clearButton, infoButton);
        return toolbar;
    }

    private void createNode(double x, double y) {
        String label = "N" + nodeCounter++;
        GraphNode node = new GraphNode(label, x - 25, y - 25);
        graph.addNode(node);
        LoggerSingleton.getInstance().log("Noeud " + label + " cree");
    }

    private void handleEdgeCreation(double x, double y, Label infoLabel) {
        GraphNode clickedNode = graph.findNodeAt(x, y);

        if (clickedNode != null) {
            if (tempNode == null) {
                tempNode = clickedNode;
                tempNode.setSelected(true);
                infoLabel
                        .setText("Premier noeud selectionne: " + tempNode.getId() + " - Cliquez sur un autre noeud");
                LoggerSingleton.getInstance().log("Premier noeud selectionne: " + tempNode.getId());
            } else if (!tempNode.equals(clickedNode)) {
                // Créer l'arête
                GraphEdge edge = new GraphEdge(tempNode, clickedNode);
                graph.addEdge(edge);
                infoLabel.setText("Arete creee entre " + tempNode.getId() + " et " + clickedNode.getId() +
                        " (poids: " + String.format("%.1f", edge.getWeight()) + ")");
                LoggerSingleton.getInstance().log("Arete creee entre " + tempNode.getId() +
                        " et " + clickedNode.getId() +
                        " (poids: " + String.format("%.1f", edge.getWeight()) + ")");
                tempNode.setSelected(false);
                tempNode = null;
            } else {
                infoLabel.setText("Impossible de connecter un noeud a lui-meme");
            }
        } else {
            infoLabel.setText("Cliquez sur un noeud pour creer une arete");
        }
    }

    private void selectNode(double x, double y, Label infoLabel) {
        GraphNode clickedNode = graph.findNodeAt(x, y);

        if (clickedNode != null) {
            if (selectedStartNode == null) {
                selectedStartNode = clickedNode;
                selectedStartNode.setSelected(true);
                infoLabel.setText("Noeud de depart selectionne: " + selectedStartNode.getId()
                        + " - Selectionnez le noeud d'arrivee");
                LoggerSingleton.getInstance().log("Noeud de depart selectionne: " + selectedStartNode.getId());
            } else if (selectedEndNode == null && !clickedNode.equals(selectedStartNode)) {
                selectedEndNode = clickedNode;
                selectedEndNode.setSelected(true);
                infoLabel.setText("Noeud d'arrivee selectionne: " + selectedEndNode.getId()
                        + " - Cliquez sur 'Calculer plus court chemin'");
                LoggerSingleton.getInstance().log("Noeud d'arrivee selectionne: " + selectedEndNode.getId());
            } else if (clickedNode.equals(selectedStartNode)) {
                infoLabel.setText("Ce noeud est deja selectionne comme depart");
            } else {
                clearSelections();
                selectedStartNode = clickedNode;
                selectedStartNode.setSelected(true);
                infoLabel.setText("Nouvelle selection - Noeud de depart: " + selectedStartNode.getId()
                        + " - Selectionnez le noeud d'arrivee");
                LoggerSingleton.getInstance()
                        .log("Nouvelle selection - Noeud de depart: " + selectedStartNode.getId());
            }
        } else {
            infoLabel.setText("Cliquez sur un noeud pour le selectionner");
        }
    }

    private void clearSelections() {
        if (selectedStartNode != null) {
            selectedStartNode.setSelected(false);
            selectedStartNode = null;
        }
        if (selectedEndNode != null) {
            selectedEndNode.setSelected(false);
            selectedEndNode = null;
        }
        if (tempNode != null) {
            tempNode.setSelected(false);
            tempNode = null;
        }
    }

    private void updateStatusLabel(Label statusLabel) {
        String startText = selectedStartNode != null ? selectedStartNode.getId() : "Aucun";
        String endText = selectedEndNode != null ? selectedEndNode.getId() : "Aucun";
        statusLabel.setText("Noeud debut: " + startText + " | Noeud fin: " + endText);
    }

    private void calculateShortestPath() {
        if (selectedStartNode == null || selectedEndNode == null) {
            showAlert("Veuillez selectionner un noeud de depart et un noeud d'arrivee");
            return;
        }

        List<GraphNode> path = graph.findShortestPath(selectedStartNode, selectedEndNode);

        if (path.isEmpty()) {
            showAlert("Aucun chemin trouve entre " + selectedStartNode.getId() +
                    " et " + selectedEndNode.getId());
            LoggerSingleton.getInstance().log("Aucun chemin trouve");
        } else {
            graph.highlightPath(path);
            StringBuilder pathStr = new StringBuilder();
            for (int i = 0; i < path.size(); i++) {
                pathStr.append(path.get(i).getId());
                if (i < path.size() - 1)
                    pathStr.append(" -> ");
            }
            LoggerSingleton.getInstance().log("Plus court chemin trouve: " + pathStr.toString());
            showAlert("Plus court chemin trouve: " + pathStr.toString());
        }

        redrawGraph();
    }

    private void redrawGraph() {
        gc.clearRect(0, 0, canvas.getWidth(), canvas.getHeight());

        // Dessiner les arêtes en premier
        for (GraphEdge edge : graph.getEdges()) {
            edge.draw(gc);
        }

        // Dessiner les nœuds par-dessus
        for (GraphNode node : graph.getNodes()) {
            node.draw(gc);
        }
    }

    private void showGraphInfo() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Information sur le graphe");
        alert.setHeaderText("Statistiques du graphe");
        alert.setContentText("Nombre de noeuds: " + graph.getNodeCount() + "\n" +
                "Nombre d'aretes: " + graph.getEdgeCount());
        alert.showAndWait();
    }

    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Attention");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
