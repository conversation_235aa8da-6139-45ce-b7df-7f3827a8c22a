package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;

/**
 * Décorateur abstrait pour les formes
 * Version corrigée qui hérite de Shape au lieu d'implémenter ShapeAdapter
 */
public abstract class ShapeDecorator extends Shape {
    protected Shape decoratedShape;

    public ShapeDecorator(Shape decoratedShape) {
        super(decoratedShape.getX(), decoratedShape.getY(), 
              decoratedShape.getWidth(), decoratedShape.getHeight(), 
              decoratedShape.getColor());
        this.decoratedShape = decoratedShape;
    }

    @Override
    public void draw(GraphicsContext gc) {
        decoratedShape.draw(gc);
    }
    
    @Override
    public boolean contains(double x, double y) {
        return decoratedShape.contains(x, y);
    }
    
    @Override
    public String serialize() {
        return decoratedShape.serialize();
    }
    
    @Override
    public String getType() {
        return decoratedShape.getType();
    }
    
    public Shape getDecoratedShape() {
        return decoratedShape;
    }
}

