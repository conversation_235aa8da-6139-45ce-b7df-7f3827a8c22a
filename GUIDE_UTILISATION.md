# 🚀 Guide d'Utilisation - Applications JavaFX Design Patterns

## 📋 Prérequis

1. **Java 17 ou supérieur** installé
2. **JavaFX 21** téléchargé (déjà configuré dans votre projet)
3. **Eclipse IDE** (optionnel mais recommandé)

## 🎯 Méthodes d'Exécution

### **Méthode 1 : Scripts Batch (Recommandée)**

#### **Application de Dessin**
1. Double-cliquez sur `run_drawing_app.bat`
2. L'application se compile et se lance automatiquement

#### **Application Graphe**
1. Double-cliquez sur `run_graph_app.bat`
2. L'application se compile et se lance automatiquement

### **Méthode 2 : Depuis Eclipse**

1. **Ouvrir Eclipse** et importer le projet
2. **Clic droit** sur `DrawingApplication.java` ou `GraphApplication.java`
3. **Run As** → **Java Application**

Si cela ne fonctionne pas :
4. **Run As** → **Run Configurations...**
5. Dans **VM arguments**, ajouter :
```
--module-path "C:/Users/<USER>/Downloads/openjfx-21.0.7_windows-x64_bin-sdk/javafx-sdk-21.0.7/lib" --add-modules javafx.controls,javafx.graphics
```

### **Méthode 3 : Ligne de Commande**

#### **Compilation**
```bash
javac --module-path "C:/Users/<USER>/Downloads/openjfx-21.0.7_windows-x64_bin-sdk/javafx-sdk-21.0.7/lib" --add-modules javafx.controls,javafx.graphics -d bin src/designPattern_MiniProjet/*.java src/module-info.java
```

#### **Exécution Application Dessin**
```bash
java --module-path "C:/Users/<USER>/Downloads/openjfx-21.0.7_windows-x64_bin-sdk/javafx-sdk-21.0.7/lib" --add-modules javafx.controls,javafx.graphics -cp bin designPattern_MiniProjet.DrawingApplication
```

#### **Exécution Application Graphe**
```bash
java --module-path "C:/Users/<USER>/Downloads/openjfx-21.0.7_windows-x64_bin-sdk/javafx-sdk-21.0.7/lib" --add-modules javafx.controls,javafx.graphics -cp bin designPattern_MiniProjet.GraphApplication
```

## 🎨 Application de Dessin - Mode d'Emploi

### **Interface Utilisateur**

L'application contient 3 barres d'outils :

#### **1. Barre de Formes**
- **Rectangle** : Sélectionne l'outil rectangle
- **Cercle** : Sélectionne l'outil cercle  
- **Ligne** : Sélectionne l'outil ligne

#### **2. Barre d'Actions**
- **Enregistrer** : Sauvegarde le dessin
- **Charger** : Charge un dessin existant
- **Effacer** : Efface tout le dessin

#### **3. Barre d'Options**
- **Décorateur** : Case à cocher pour activer les bordures épaisses
- **Journalisation** : Menu déroulant (Console, Fichier, Base de données)

### **Utilisation**

1. **Sélectionner une forme** dans la barre de formes
2. **Cliquer sur la zone de dessin** pour créer la forme
3. **Activer le décorateur** pour ajouter des bordures
4. **Changer la journalisation** selon vos préférences
5. **Sauvegarder/Charger** vos créations

## 📊 Application Graphe - Mode d'Emploi

### **Interface Utilisateur**

L'application contient 3 barres d'outils et 2 zones d'information :

#### **1. Barre de Modes**
- **Créer nœuds** : Mode création de nœuds (par défaut)
- **Créer arêtes** : Mode création d'arêtes entre nœuds
- **Sélectionner** : Mode sélection pour le calcul de chemin

#### **2. Barre d'Algorithmes**
- **Algorithme** : Menu déroulant (Dijkstra, Bellman-Ford)
- **Calculer plus court chemin** : Lance le calcul
- **Effacer chemin** : Efface la mise en surbrillance

#### **3. Barre d'Actions**
- **Effacer tout** : Efface tout le graphe
- **Info graphe** : Affiche les statistiques

#### **4. Zones d'Information**
- **Zone d'info** : Instructions contextuelles
- **Zone de statut** : Nœuds sélectionnés (début/fin)

### **Utilisation Étape par Étape**

#### **Étape 1 : Créer des Nœuds**
1. **Mode "Créer nœuds"** activé par défaut
2. **Cliquer** sur la zone de dessin pour créer des nœuds
3. Les nœuds sont automatiquement étiquetés (N1, N2, N3...)

#### **Étape 2 : Créer des Arêtes**
1. **Sélectionner "Créer arêtes"**
2. **Cliquer sur le premier nœud** (il devient vert)
3. **Cliquer sur le second nœud** pour créer l'arête
4. Le poids est calculé automatiquement (distance euclidienne)

#### **Étape 3 : Calculer le Plus Court Chemin**
1. **Sélectionner "Sélectionner"**
2. **Cliquer sur le nœud de départ** (il devient vert)
3. **Cliquer sur le nœud d'arrivée** (il devient vert aussi)
4. **Choisir l'algorithme** (Dijkstra ou Bellman-Ford)
5. **Cliquer "Calculer plus court chemin"**
6. Le chemin s'affiche en rouge avec les nœuds en jaune

### **Codes Couleur**

- **Bleu clair** : Nœuds normaux
- **Vert** : Nœuds sélectionnés
- **Jaune** : Nœuds dans le plus court chemin
- **Noir** : Arêtes normales
- **Rouge** : Arêtes dans le plus court chemin

## 🔧 Résolution de Problèmes

### **Problème : Application ne se lance pas**
**Solution** : Vérifiez que JavaFX est correctement configuré
```bash
java --version
```

### **Problème : Erreur de compilation**
**Solution** : Vérifiez le chemin vers JavaFX dans les scripts batch

### **Problème : Caractères mal affichés**
**Solution** : Les caractères spéciaux ont été remplacés par des équivalents ASCII

### **Problème : Impossible de créer des arêtes**
**Solutions** :
1. Vérifiez que vous êtes en mode "Créer arêtes"
2. Cliquez bien sur les nœuds (pas à côté)
3. Sélectionnez deux nœuds différents

### **Problème : Calcul de chemin ne fonctionne pas**
**Solutions** :
1. Vérifiez que vous êtes en mode "Sélectionner"
2. Sélectionnez un nœud de départ ET un nœud d'arrivée
3. Assurez-vous qu'il existe un chemin entre les nœuds

## 📚 Fonctionnalités Avancées

### **Journalisation**
- **Console** : Messages dans la console Eclipse/terminal
- **Fichier** : Messages sauvés dans `log.txt`
- **Base de données** : Messages sauvés en BDD (nécessite MySQL)

### **Persistance**
- **Fichier** : Sauvegarde dans `shapes.dat`
- **Base de données** : Sauvegarde en BDD (nécessite configuration)

### **Algorithmes**
- **Dijkstra** : Optimal pour poids positifs, plus rapide
- **Bellman-Ford** : Gère les poids négatifs, détecte les cycles

## 🎯 Conseils d'Utilisation

### **Pour l'Application de Dessin**
1. Testez différentes combinaisons de formes
2. Activez le décorateur pour voir l'effet
3. Changez la stratégie de logging pour voir les différences
4. Sauvegardez et rechargez pour tester la persistance

### **Pour l'Application Graphe**
1. Créez d'abord plusieurs nœuds
2. Connectez-les avec des arêtes
3. Testez les deux algorithmes sur le même graphe
4. Créez des graphes complexes pour voir les différences

## 🚀 Démonstration Recommandée

1. **Lancez l'application de dessin**
2. **Créez quelques formes** avec et sans décorateur
3. **Changez la stratégie de logging**
4. **Sauvegardez et rechargez**
5. **Lancez l'application graphe**
6. **Créez un graphe simple** (4-5 nœuds)
7. **Testez les algorithmes** de plus court chemin
8. **Comparez Dijkstra et Bellman-Ford**

Cette démonstration montre tous les design patterns en action ! 🎉
