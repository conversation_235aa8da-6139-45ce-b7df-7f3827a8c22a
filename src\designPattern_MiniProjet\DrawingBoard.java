package designPattern_MiniProjet;

import java.util.ArrayList;
import java.util.List;

/**
 * Singleton DrawingBoard qui implémente aussi le pattern Observer
 * Gère l'état global du dessin et notifie les observateurs
 * Version thread-safe
 */
public class DrawingBoard implements DrawingSubject {
    private static volatile DrawingBoard instance;
    private List<DrawingObserver> observers = new ArrayList<>();
    private List<Shape> shapes = new ArrayList<>();

    // Constructeur privé pour le pattern Singleton
    private DrawingBoard() {
    }

    // Méthode getInstance thread-safe pour le pattern Singleton
    public static DrawingBoard getInstance() {
        if (instance == null) {
            synchronized (DrawingBoard.class) {
                if (instance == null) {
                    instance = new DrawingBoard();
                }
            }
        }
        return instance;
    }

    @Override
    public void attach(DrawingObserver observer) {
        synchronized (observers) {
            observers.add(observer);
        }
    }

    // Alias pour attach() pour compatibilité
    public void addObserver(DrawingObserver observer) {
        attach(observer);
    }    @Override
    public void detach(DrawingObserver observer) {
        synchronized (observers) {
            observers.remove(observer);
        }
    }

    // Alias pour detach() pour compatibilité
    public void removeObserver(DrawingObserver observer) {
        detach(observer);
    }

    @Override
    public void notifyObservers(String message) {
        List<DrawingObserver> observersCopy;
        synchronized (observers) {
            observersCopy = new ArrayList<>(observers);
        }
        for (DrawingObserver observer : observersCopy) {
            observer.update(message);
        }
    }

    public void addShape(Shape shape) {
        synchronized (shapes) {
            shapes.add(shape);
        }
        notifyObservers("Shape added: " + shape.getType());
    }

    public List<Shape> getShapes() {
        synchronized (shapes) {
            return new ArrayList<>(shapes);
        }
    }

    public void setShapes(List<Shape> shapes) {
        synchronized (this.shapes) {
            this.shapes.clear();
            this.shapes.addAll(shapes);
        }
        notifyObservers("Shapes updated");
    }    public void clearShapes() {
        synchronized (shapes) {
            shapes.clear();
        }
        notifyObservers("All shapes cleared");
    }
}
