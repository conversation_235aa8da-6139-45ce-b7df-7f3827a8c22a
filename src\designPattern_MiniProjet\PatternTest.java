package designPattern_MiniProjet;

/**
 * Test simple pour vérifier les corrections apportées aux design patterns
 * Sans interface graphique pour éviter les problèmes de JavaFX
 */
public class PatternTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test des Design Patterns Corrigés ===\n");
        
        // Test du Singleton thread-safe
        testSingleton();
        
        // Test du Factory Pattern
        testFactory();
        
        // Test de la hiérarchie Shape
        testShapeHierarchy();
        
        // Test du Observer Pattern
        testObserver();
        
        System.out.println("\n=== Tous les tests sont passés avec succès! ===");
    }
    
    private static void testSingleton() {
        System.out.println("1. Test Singleton Pattern:");
        
        // Test LoggerSingleton
        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();
        
        if (logger1 == logger2) {
            System.out.println("   ✓ LoggerSingleton: Même instance retournée");
        } else {
            System.out.println("   ✗ LoggerSingleton: Échec - instances différentes");
        }
        
        // Test DrawingBoard
        DrawingBoard board1 = DrawingBoard.getInstance();
        DrawingBoard board2 = DrawingBoard.getInstance();
        
        if (board1 == board2) {
            System.out.println("   ✓ DrawingBoard: Même instance retournée");
        } else {
            System.out.println("   ✗ DrawingBoard: Échec - instances différentes");
        }
    }
    
    private static void testFactory() {
        System.out.println("\n2. Test Factory Pattern:");
        
        try {
            Shape circle = ShapeFactory.createShape("circle", 10, 10);
            Shape rectangle = ShapeFactory.createShape("rectangle", 20, 20);
            Shape line = ShapeFactory.createShape("line", 30, 30);
            
            System.out.println("   ✓ Création de Circle: " + circle.getClass().getSimpleName());
            System.out.println("   ✓ Création de Rectangle: " + rectangle.getClass().getSimpleName());
            System.out.println("   ✓ Création de Line: " + line.getClass().getSimpleName());
        } catch (Exception e) {
            System.out.println("   ✗ Erreur Factory: " + e.getMessage());
        }
    }
    
    private static void testShapeHierarchy() {
        System.out.println("\n3. Test Hiérarchie Shape:");
        
        Circle circle = new Circle(50, 50, 25, javafx.scene.paint.Color.RED);
        Rectangle rectangle = new Rectangle(100, 100, 50, 30, javafx.scene.paint.Color.BLUE);
        Line line = new Line(0, 0, 100, 100, javafx.scene.paint.Color.GREEN);
        
        System.out.println("   ✓ Circle créé: position(" + circle.getX() + ", " + circle.getY() + ")");
        System.out.println("   ✓ Rectangle créé: position(" + rectangle.getX() + ", " + rectangle.getY() + ")");
        System.out.println("   ✓ Line créée: de(" + line.getX() + ", " + line.getY() + ")");
        
        // Test Decorator
        BorderDecorator decoratedCircle = new BorderDecorator(circle);
        System.out.println("   ✓ BorderDecorator appliqué au cercle");
    }
    
    private static void testObserver() {
        System.out.println("\n4. Test Observer Pattern:");
        
        DrawingBoard board = DrawingBoard.getInstance();
        
        // Créer un observateur de test
        TestObserver observer = new TestObserver();
        board.addObserver(observer);
        
        // Déclencher une notification
        Shape testShape = new Circle(25, 25, 10, javafx.scene.paint.Color.YELLOW);
        board.addShape(testShape);
        
        if (observer.wasNotified()) {
            System.out.println("   ✓ Observer notifié correctement");
        } else {
            System.out.println("   ✗ Observer non notifié");
        }
    }
    
    // Classe d'aide pour tester l'Observer
    private static class TestObserver implements DrawingObserver {
        private boolean notified = false;
        
        @Override
        public void update(String message) {
            notified = true;
            System.out.println("   ✓ Notification reçue: " + message);
        }
        
        public boolean wasNotified() {
            return notified;
        }
    }
}
