package designPattern_MiniProjet;

/**
 * Interface Subject pour le pattern Observer
 * Version améliorée avec message contextuel
 */
public interface DrawingSubject {
    void attach(DrawingObserver observer);
    void detach(DrawingObserver observer);
    void notifyObservers(String message);
    
    // Méthode de compatibilité
    default void notifyObservers() {
        notifyObservers("State changed");
    }
}
