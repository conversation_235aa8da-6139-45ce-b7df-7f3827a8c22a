package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe concrète Line
 * Version corrigée utilisant le pattern Strategy
 */
public class Line extends Shape {
    private double endX, endY;
    
    public Line(double startX, double startY, double endX, double endY, Color color) {
        super(startX, startY, Math.abs(endX - startX), Math.abs(endY - startY), color);
        this.endX = endX;
        this.endY = endY;
        this.drawingStrategy = new LineDrawingStrategy();
    }
    
    public Line(double startX, double startY, double endX, double endY) {
        this(startX, startY, endX, endY, Color.BLACK);
    }
    
    public Line(double x, double y) {
        this(x, y, x + 100, y + 50); // Ligne par défaut
    }
    
    public double getEndX() {
        return endX;
    }
    
    public double getEndY() {
        return endY;
    }
    
    public void setEndPoint(double endX, double endY) {
        this.endX = endX;
        this.endY = endY;
        this.width = Math.abs(endX - x);
        this.height = Math.abs(endY - y);
    }
    
    @Override
    public boolean contains(double pointX, double pointY) {
        // Distance point-ligne approximative
        double tolerance = 5.0;
        double A = endY - y;
        double B = x - endX;
        double C = endX * y - x * endY;
        
        double distance = Math.abs(A * pointX + B * pointY + C) / Math.sqrt(A * A + B * B);
        return distance <= tolerance;
    }
    
    @Override
    public String serialize() {
        return String.format("LINE,%.2f,%.2f,%.2f,%.2f,%s", x, y, endX, endY, color.toString());
    }
    
    @Override
    public String getType() {
        return "Line";
    }
    
    // Factory method pour créer depuis une chaîne sérialisée
    public static Line deserialize(String data) {
        String[] parts = data.split(",");
        if (parts.length >= 5 && "LINE".equals(parts[0])) {
            double startX = Double.parseDouble(parts[1]);
            double startY = Double.parseDouble(parts[2]);
            double endX = Double.parseDouble(parts[3]);
            double endY = Double.parseDouble(parts[4]);
            Color color = parts.length > 5 ? Color.valueOf(parts[5]) : Color.BLACK;
            return new Line(startX, startY, endX, endY, color);
        }
        throw new IllegalArgumentException("Invalid line data: " + data);
    }
}
