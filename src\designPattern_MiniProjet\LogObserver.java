package designPattern_MiniProjet;

/**
 * Observateur concret qui log les changements
 * Version améliorée avec messages contextuels
 */
public class LogObserver implements DrawingObserver {
    
    @Override
    public void update(String message) {
        LoggerSingleton.getInstance().log("Drawing event: " + message);
    }
    
    @Override
    public void update() {
        update("Drawing updated");
    }
}
