package designPattern_MiniProjet;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;

public class FileLogger implements LoggerStrategy {
	  public void log(String message) {
	        try (PrintWriter out = new PrintWriter(new FileWriter("log.txt", true))) {
	            out.println("[LOG]: " + message);
	        } catch (IOException e) {
	            System.err.println("Erreur lors de l'écriture dans le fichier : " + e.getMessage());
	        }
	    }
}