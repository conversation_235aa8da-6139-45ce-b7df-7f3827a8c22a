package designPattern_MiniProjet;

import designPattern_MiniProjet.graph.GraphNode;
import java.util.*;

/**
 * Implémentation de l'algorithme de Dijkstra
 * Strategy concrète pour le calcul du plus court chemin
 */
public class DijkstraStrategy implements ShortestPathStrategy {
    
    @Override
    public List<GraphNode> findShortestPath(Graph graph, GraphNode start, GraphNode end) {
        if (start == null || end == null) {
            return new ArrayList<>();
        }
        
        if (start.equals(end)) {
            List<GraphNode> path = new ArrayList<>();
            path.add(start);
            return path;
        }
        
        // Initialisation
        Map<GraphNode, Double> distances = new HashMap<>();
        Map<GraphNode, GraphNode> previous = new HashMap<>();
        Set<GraphNode> visited = new HashSet<>();
        PriorityQueue<NodeDistance> queue = new PriorityQueue<>();
        
        // Initialiser les distances
        for (GraphNode node : graph.getNodes()) {
            distances.put(node, Double.POSITIVE_INFINITY);
        }
        distances.put(start, 0.0);
        queue.offer(new NodeDistance(start, 0.0));
        
        while (!queue.isEmpty()) {
            NodeDistance current = queue.poll();
            GraphNode currentNode = current.node;
            
            if (visited.contains(currentNode)) {
                continue;
            }
            
            visited.add(currentNode);
            
            // Si on a atteint la destination
            if (currentNode.equals(end)) {
                break;
            }
            
            // Examiner les voisins
            Map<GraphNode, Double> neighbors = graph.getNeighbors(currentNode);
            for (Map.Entry<GraphNode, Double> neighbor : neighbors.entrySet()) {
                GraphNode neighborNode = neighbor.getKey();
                double edgeWeight = neighbor.getValue();
                
                if (visited.contains(neighborNode)) {
                    continue;
                }
                
                double newDistance = distances.get(currentNode) + edgeWeight;
                
                if (newDistance < distances.get(neighborNode)) {
                    distances.put(neighborNode, newDistance);
                    previous.put(neighborNode, currentNode);
                    queue.offer(new NodeDistance(neighborNode, newDistance));
                }
            }
        }
        
        // Reconstruire le chemin
        return reconstructPath(previous, start, end);
    }
    
    private List<GraphNode> reconstructPath(Map<GraphNode, GraphNode> previous, 
                                          GraphNode start, GraphNode end) {
        List<GraphNode> path = new ArrayList<>();
        GraphNode current = end;
        
        // Remonter le chemin depuis la fin
        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }
        
        // Vérifier si un chemin existe
        if (path.isEmpty() || !path.get(0).equals(start)) {
            return new ArrayList<>(); // Aucun chemin trouvé
        }
        
        return path;
    }
    
    @Override
    public String getAlgorithmName() {
        return "Dijkstra";
    }
    
    /**
     * Classe interne pour représenter un nœud avec sa distance
     */
    private static class NodeDistance implements Comparable<NodeDistance> {
        final GraphNode node;
        final double distance;
        
        NodeDistance(GraphNode node, double distance) {
            this.node = node;
            this.distance = distance;
        }
        
        @Override
        public int compareTo(NodeDistance other) {
            return Double.compare(this.distance, other.distance);
        }
    }
}
