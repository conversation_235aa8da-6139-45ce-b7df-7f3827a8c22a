package designPattern_MiniProjet;

import java.util.*;

/**
 * Implémentation de l'algorithme de Bellman-Ford
 * Strategy concrète pour le calcul du plus court chemin
 * Peut gérer les poids négatifs (contrairement à Dijkstra)
 */
public class BellmanFordStrategy implements ShortestPathStrategy {

    @Override
    public List<GraphNode> findShortestPath(Graph graph, GraphNode start, GraphNode end) {
        if (start == null || end == null) {
            return new ArrayList<>();
        }

        if (start.equals(end)) {
            List<GraphNode> path = new ArrayList<>();
            path.add(start);
            return path;
        }

        List<GraphNode> nodes = graph.getNodes();
        List<GraphEdge> edges = graph.getEdges();

        // Initialisation
        Map<GraphNode, Double> distances = new HashMap<>();
        Map<GraphNode, GraphNode> previous = new HashMap<>();

        for (GraphNode node : nodes) {
            distances.put(node, Double.POSITIVE_INFINITY);
        }
        distances.put(start, 0.0);

        // Relaxation des arêtes (V-1 fois)
        for (int i = 0; i < nodes.size() - 1; i++) {
            boolean updated = false;

            for (GraphEdge edge : edges) {
                GraphNode u = edge.getStartNode();
                GraphNode v = edge.getEndNode();
                double weight = edge.getWeight();

                // Relaxation dans les deux sens (graphe non orienté)
                if (relax(distances, previous, u, v, weight)) {
                    updated = true;
                }
                if (relax(distances, previous, v, u, weight)) {
                    updated = true;
                }
            }

            // Si aucune mise à jour, on peut arrêter plus tôt
            if (!updated) {
                break;
            }
        }

        // Vérification des cycles négatifs
        for (GraphEdge edge : edges) {
            GraphNode u = edge.getStartNode();
            GraphNode v = edge.getEndNode();
            double weight = edge.getWeight();

            if (distances.get(u) != Double.POSITIVE_INFINITY &&
                    distances.get(u) + weight < distances.get(v)) {
                // Cycle négatif détecté
                LoggerSingleton.getInstance().log("Cycle négatif détecté dans le graphe");
                return new ArrayList<>();
            }

            if (distances.get(v) != Double.POSITIVE_INFINITY &&
                    distances.get(v) + weight < distances.get(u)) {
                // Cycle négatif détecté
                LoggerSingleton.getInstance().log("Cycle négatif détecté dans le graphe");
                return new ArrayList<>();
            }
        }

        // Reconstruire le chemin
        return reconstructPath(previous, start, end);
    }

    private boolean relax(Map<GraphNode, Double> distances,
            Map<GraphNode, GraphNode> previous,
            GraphNode u, GraphNode v, double weight) {
        if (distances.get(u) != Double.POSITIVE_INFINITY &&
                distances.get(u) + weight < distances.get(v)) {
            distances.put(v, distances.get(u) + weight);
            previous.put(v, u);
            return true;
        }
        return false;
    }

    private List<GraphNode> reconstructPath(Map<GraphNode, GraphNode> previous,
            GraphNode start, GraphNode end) {
        List<GraphNode> path = new ArrayList<>();
        GraphNode current = end;

        // Remonter le chemin depuis la fin
        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }

        // Vérifier si un chemin existe
        if (path.isEmpty() || !path.get(0).equals(start)) {
            return new ArrayList<>(); // Aucun chemin trouvé
        }

        return path;
    }

    @Override
    public String getAlgorithmName() {
        return "Bellman-Ford";
    }
}
