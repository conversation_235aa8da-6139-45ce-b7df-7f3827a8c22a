package designPattern_MiniProjet;

/**
 * Test simple pour vérifier les design patterns sans dépendances JavaFX
 */
public class SimplePatternTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test Simplified Design Patterns ===\n");
        
        // Test du Singleton thread-safe
        testSingleton();
        
        // Test du Observer Pattern
        testObserver();
        
        System.out.println("\n=== Tests basiques réussis! ===");
        System.out.println("Note: Les tests complets nécessitent JavaFX pour les interfaces graphiques.");
    }
    
    private static void testSingleton() {
        System.out.println("1. Test Singleton Pattern:");
        
        // Test LoggerSingleton
        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();
        
        if (logger1 == logger2) {
            System.out.println("   ✓ LoggerSingleton: Même instance retournée");
        } else {
            System.out.println("   ✗ LoggerSingleton: Échec - instances différentes");
        }
        
        // Test thread safety basique
        logger1.setStrategy(new ConsoleLogger());
        logger1.log("Test message singleton");
        
        // Test DrawingBoard
        DrawingBoard board1 = DrawingBoard.getInstance();
        DrawingBoard board2 = DrawingBoard.getInstance();
        
        if (board1 == board2) {
            System.out.println("   ✓ DrawingBoard: Même instance retournée");
        } else {
            System.out.println("   ✗ DrawingBoard: Échec - instances différentes");
        }
    }
    
    private static void testObserver() {
        System.out.println("\n2. Test Observer Pattern:");
        
        DrawingBoard board = DrawingBoard.getInstance();
        
        // Créer un observateur de test
        TestObserver observer = new TestObserver();
        board.addObserver(observer);
        
        // Déclencher une notification
        board.clearShapes(); // Ceci devrait notifier l'observer
        
        if (observer.wasNotified()) {
            System.out.println("   ✓ Observer notifié correctement");
        } else {
            System.out.println("   ✗ Observer non notifié");
        }
        
        board.removeObserver(observer);
        observer.reset();
        board.clearShapes(); // Ne devrait plus notifier
        
        if (!observer.wasNotified()) {
            System.out.println("   ✓ Observer correctement supprimé");
        } else {
            System.out.println("   ✗ Observer toujours notifié après suppression");
        }
    }
    
    // Classe d'aide pour tester l'Observer
    private static class TestObserver implements DrawingObserver {
        private boolean notified = false;
        
        @Override
        public void update(String message) {
            notified = true;
            System.out.println("   ✓ Notification reçue: " + message);
        }
        
        public boolean wasNotified() {
            return notified;
        }
        
        public void reset() {
            notified = false;
        }
    }
}
