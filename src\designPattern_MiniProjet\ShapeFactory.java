package designPattern_MiniProjet;

import javafx.scene.paint.Color;

/**
 * Factory pour créer des objets de manière polymorphique
 * Résout le problème des constructions conditionnelles
 * Implémente le pattern Factory Method
 */
public class ShapeFactory {

    // Méthode pour créer un cercle (utilisée dans UnifiedSingleViewApplication)
    public static Shape createShape(String type, double x, double y, Color color) {
        switch (type.toUpperCase()) {
            case "CIRCLE":
                return new Circle(x + 25, y + 25, 25, color); // Centre + rayon
            case "RECTANGLE":
                return new Rectangle(x, y, 50, 50, color); // Taille par défaut
            case "LINE":
                return new Line(x, y, x + 50, y + 50, color); // Ligne par défaut
            default:
                throw new IllegalArgumentException("Type non supporté: " + type);
        }
    }

    // Méthode pour créer avec dimensions spécifiques
    public static Shape createShapeWithDimensions(String type, double x, double y, double width, double height,
            Color color) {
        switch (type.toUpperCase()) {
            case "RECTANGLE":
                return new Rectangle(x, y, width, height, color);
            case "CIRCLE":
                double radius = Math.min(width, height) / 2;
                return new Circle(x + radius, y + radius, radius, color);
            case "LINE":
                return new Line(x, y, x + width, y + height, color);
            default:
                throw new IllegalArgumentException("Type non supporté: " + type);
        }
    }

    // Méthode pour créer une ligne avec points spécifiques
    public static Shape createLine(double startX, double startY, double endX, double endY, Color color) {
        return new Line(startX, startY, endX, endY, color);
    }

    // Méthode de compatibilité
    public static Shape createShape(String type, double x, double y) {
        return createShape(type, x, y, Color.BLUE);
    }

    // Factory pour les loggers
    public static LoggerStrategy createLogger(String type) {
        switch (type.toLowerCase()) {
            case "console":
                return new ConsoleLogger();
            case "file":
                return new FileLogger();
            case "database":
                return new DBLogger();
            default:
                return new ConsoleLogger(); // Default
        }
    }
}
