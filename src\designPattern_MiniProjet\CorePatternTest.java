package designPattern_MiniProjet;

/**
 * Test ultra-simple pour vérifier les design patterns de base
 * Sans aucune dépendance JavaFX
 */
public class CorePatternTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test des Design Patterns Core ===\n");
        
        // Test du Singleton thread-safe
        testSingleton();
        
        // Test du Observer Pattern
        testObserver();
        
        System.out.println("\n=== Tests core réussis! ===");
    }
    
    private static void testSingleton() {
        System.out.println("1. Test Singleton Pattern:");
        
        // Test LoggerSingleton
        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();
        
        if (logger1 == logger2) {
            System.out.println("   ✓ LoggerSingleton: Thread-safe singleton fonctionne");
        } else {
            System.out.println("   ✗ LoggerSingleton: Échec");
        }
        
        // Test stratégie de logging
        logger1.setStrategy(new ConsoleLogger());
        System.out.print("   ");
        logger1.log("Test du pattern Strategy dans Singleton");
        
        // Test DrawingBoard singleton
        DrawingBoard board1 = DrawingBoard.getInstance();
        DrawingBoard board2 = DrawingBoard.getInstance();
        
        if (board1 == board2) {
            System.out.println("   ✓ DrawingBoard: Thread-safe singleton fonctionne");
        } else {
            System.out.println("   ✗ DrawingBoard: Échec");
        }
    }
    
    private static void testObserver() {
        System.out.println("\n2. Test Observer Pattern:");
        
        DrawingBoard board = DrawingBoard.getInstance();
        
        // Créer des observateurs de test
        TestObserver observer1 = new TestObserver("Observer1");
        TestObserver observer2 = new TestObserver("Observer2");
        
        // Attacher les observateurs
        board.addObserver(observer1);
        board.addObserver(observer2);
        
        // Déclencher une notification
        board.notifyObservers("Test notification");
        
        if (observer1.wasNotified() && observer2.wasNotified()) {
            System.out.println("   ✓ Tous les observers ont été notifiés");
        } else {
            System.out.println("   ✗ Échec de notification des observers");
        }
        
        // Test de suppression d'observer
        board.removeObserver(observer1);
        observer1.reset();
        observer2.reset();
        
        board.notifyObservers("Test après suppression");
        
        if (!observer1.wasNotified() && observer2.wasNotified()) {
            System.out.println("   ✓ Suppression d'observer fonctionne correctement");
        } else {
            System.out.println("   ✗ Échec de suppression d'observer");
        }
        
        // Nettoyer
        board.removeObserver(observer2);
    }
    
    // Classe d'aide pour tester l'Observer
    private static class TestObserver implements DrawingObserver {
        private boolean notified = false;
        private String name;
        
        public TestObserver(String name) {
            this.name = name;
        }
        
        @Override
        public void update(String message) {
            notified = true;
            System.out.println("   ✓ " + name + " a reçu: " + message);
        }
        
        public boolean wasNotified() {
            return notified;
        }
        
        public void reset() {
            notified = false;
        }
    }
}
