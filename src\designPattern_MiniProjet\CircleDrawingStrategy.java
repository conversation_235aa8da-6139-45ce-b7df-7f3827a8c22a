package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Strategy concrète pour dessiner des cercles
 * Implémente le pattern Strategy - Version corrigée
 */
public class CircleDrawingStrategy implements DrawingStrategy {

    @Override
    public void draw(GraphicsContext gc, Shape shape) {
        if (shape instanceof Circle) {
            Circle circle = (Circle) shape;
            
            // Couleur de remplissage
            gc.setFill(shape.getColor());
            gc.fillOval(shape.getX(), shape.getY(), 
                       circle.getRadius() * 2, circle.getRadius() * 2);
            
            // Contour
            gc.setStroke(Color.BLACK);
            gc.setLineWidth(2);
            gc.strokeOval(shape.getX(), shape.getY(), 
                         circle.getRadius() * 2, circle.getRadius() * 2);
        }
    }
}
