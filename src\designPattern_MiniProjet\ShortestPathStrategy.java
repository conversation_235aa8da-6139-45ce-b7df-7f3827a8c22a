package designPattern_MiniProjet;

import designPattern_MiniProjet.graph.GraphNode;
import java.util.List;

/**
 * Interface Strategy pour les algorithmes de plus court chemin
 * Implémente le pattern Strategy
 */
public interface ShortestPathStrategy {
    /**
     * Trouve le plus court chemin entre deux nœuds
     * @param graph Le graphe dans lequel chercher
     * @param start Le nœud de départ
     * @param end Le nœud d'arrivée
     * @return La liste des nœuds formant le plus court chemin, ou une liste vide si aucun chemin n'existe
     */
    List<GraphNode> findShortestPath(Graph graph, GraphNode start, GraphNode end);
    
    /**
     * Retourne le nom de l'algorithme
     * @return Le nom de l'algorithme
     */
    String getAlgorithmName();
}
