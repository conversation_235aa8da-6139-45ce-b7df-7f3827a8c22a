package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Décorateur concret qui ajoute une bordure épaisse aux formes
 * Version corrigée qui utilise le polymorphisme
 */
public class BorderDecorator extends ShapeDecorator {
    private Color borderColor;
    private double borderWidth;

    public BorderDecorator(Shape decoratedShape) {
        this(decoratedShape, Color.DARKRED, 4.0);
    }

    public BorderDecorator(Shape decoratedShape, Color borderColor, double borderWidth) {
        super(decoratedShape);
        this.borderColor = borderColor;
        this.borderWidth = borderWidth;
    }

    @Override
    public void draw(GraphicsContext gc) {
        // Dessiner la forme originale
        super.draw(gc);

        // Ajouter la bordure décorative
        gc.setStroke(borderColor);
        gc.setLineWidth(borderWidth);

        // Utiliser le polymorphisme pour dessiner la bordure appropriée
        if (decoratedShape instanceof Circle) {
            Circle circle = (Circle) decoratedShape;
            double radius = circle.getRadius();
            gc.strokeOval(decoratedShape.getX() - borderWidth / 2,
                         decoratedShape.getY() - borderWidth / 2,
                         radius * 2 + borderWidth,
                         radius * 2 + borderWidth);
                } else if (decoratedShape instanceof Rectangle) {
            gc.strokeRect(decoratedShape.getX() - borderWidth / 2,
                         decoratedShape.getY() - borderWidth / 2,
                         decoratedShape.getWidth() + borderWidth,
                         decoratedShape.getHeight() + borderWidth);
        } else if (decoratedShape instanceof Line) {
            // Pour les lignes, dessiner un rectangle autour
            gc.strokeRect(decoratedShape.getX() - borderWidth / 2,
                         decoratedShape.getY() - borderWidth / 2,
                         decoratedShape.getWidth() + borderWidth,
                         decoratedShape.getHeight() + borderWidth);
        }
    }
    
    @Override
    public String getType() {
        return "Bordered " + decoratedShape.getType();
    }
}
