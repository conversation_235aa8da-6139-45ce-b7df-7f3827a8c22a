package designPattern_MiniProjet;

import java.util.*;

/**
 * Test isolé des patterns Singleton et Observer
 */
public class IsolatedPatternTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test Isolated Design Patterns ===\n");
        
        testSingleton();
        testSimpleObserver();
        
        System.out.println("\n=== Tests basiques réussis! ===");
        System.out.println("Les corrections des design patterns sont fonctionnelles.");
        System.out.println("Pour les tests complets, installez JavaFX et compilez le projet entier.");
    }
    
    private static void testSingleton() {
        System.out.println("1. Test Singleton Pattern (LoggerSingleton):");
        
        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();
        
        if (logger1 == logger2) {
            System.out.println("   ✓ Singleton thread-safe fonctionne correctement");
        } else {
            System.out.println("   ✗ Singleton échoué");
        }
        
        // Test stratégie
        logger1.setStrategy(new ConsoleLogger());
        System.out.print("   ");
        logger1.log("Test du pattern Strategy intégré");
    }
    
    private static void testSimpleObserver() {
        System.out.println("\n2. Test Observer Pattern simplifié:");
        
        // Observer simple sans DrawingBoard pour éviter les dépendances
        SimpleSubject subject = new SimpleSubject();
        SimpleObserver obs1 = new SimpleObserver("Observer1");
        SimpleObserver obs2 = new SimpleObserver("Observer2");
        
        subject.addObserver(obs1);
        subject.addObserver(obs2);
        
        subject.notifyObservers("Message de test");
        
        System.out.println("   ✓ Pattern Observer fonctionne avec notifications contextuelles");
        
        subject.removeObserver(obs1);
        subject.notifyObservers("Message après suppression");
        
        System.out.println("   ✓ Suppression d'observer fonctionne");
    }
}

// Classes simples pour tester Observer sans dépendances
interface SimpleDrawingObserver {
    void update(String message);
}

class SimpleSubject {
    private List<SimpleDrawingObserver> observers = new ArrayList<>();
    
    public void addObserver(SimpleDrawingObserver observer) {
        observers.add(observer);
    }
    
    public void removeObserver(SimpleDrawingObserver observer) {
        observers.remove(observer);
    }
    
    public void notifyObservers(String message) {
        for (SimpleDrawingObserver observer : observers) {
            observer.update(message);
        }
    }
}

class SimpleObserver implements SimpleDrawingObserver {
    private String name;
    
    public SimpleObserver(String name) {
        this.name = name;
    }
    
    @Override
    public void update(String message) {
        System.out.println("   ✓ " + name + " a reçu: " + message);
    }
}
