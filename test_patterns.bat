@echo off
echo ========================================
echo    Test Application Unifiee - Patterns
echo ========================================
echo.

cd /d "c:\Users\<USER>\eclipse-workspace\designPattern_MiniProjet"

REM Test avec le test final qui fonctionne
echo Test des patterns implementes...
javac -cp "src" -d "bin" src\designPattern_MiniProjet\FinalPatternTest.java

if %errorlevel% neq 0 (
    echo ERREUR: Compilation du test echouee
    pause
    exit /b 1
)

java -cp "bin" designPattern_MiniProjet.FinalPatternTest

echo.
echo ========================================
echo    Test de l'Application Dessin Simple
echo ========================================
echo.

REM Test de l'application de dessin originale
echo Test de l'application de dessin...
javac -cp "src" -d "bin" src\designPattern_MiniProjet\DrawingApplication.java

if %errorlevel% neq 0 (
    echo ERREUR: Compilation application dessin echouee
    pause
    exit /b 1
)

echo Compilation reussie ! Lancement necessite JavaFX...
echo.
echo Les applications sont pretes, mais JavaFX n'est pas configure.
echo Vérifiez l'installation de JavaFX sur votre système.
echo.

pause
