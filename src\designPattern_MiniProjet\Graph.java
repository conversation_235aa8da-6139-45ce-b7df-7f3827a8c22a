package designPattern_MiniProjet;

import designPattern_MiniProjet.graph.GraphNode;
import java.util.*;

/**
 * Classe représentant un graphe
 * Utilise le pattern Strategy pour les algorithmes de plus court chemin
 */
public class Graph {
    private List<GraphNode> nodes;
    private List<GraphEdge> edges;
    private ShortestPathStrategy pathStrategy;
    
    public Graph() {
        this.nodes = new ArrayList<>();
        this.edges = new ArrayList<>();
        this.pathStrategy = new DijkstraStrategy(); // Stratégie par défaut
    }
    
    public void addNode(GraphNode node) {
        if (!nodes.contains(node)) {
            nodes.add(node);
        }
    }
    
    public void addEdge(GraphEdge edge) {
        if (!edges.contains(edge)) {
            edges.add(edge);
            // S'assurer que les nœuds sont dans le graphe
            addNode(edge.getStartNode());
            addNode(edge.getEndNode());
        }
    }
    
    public void removeNode(GraphNode node) {
        // Supprimer toutes les arêtes connectées à ce nœud
        edges.removeIf(edge -> edge.getStartNode().equals(node) || edge.getEndNode().equals(node));
        nodes.remove(node);
    }
    
    public void removeEdge(GraphEdge edge) {
        edges.remove(edge);
    }
    
    public List<GraphNode> getNodes() {
        return new ArrayList<>(nodes);
    }
    
    public List<GraphEdge> getEdges() {
        return new ArrayList<>(edges);
    }
    
    public void setPathStrategy(ShortestPathStrategy strategy) {
        this.pathStrategy = strategy;
    }
    
    public List<GraphNode> findShortestPath(GraphNode start, GraphNode end) {
        if (pathStrategy == null) {
            throw new IllegalStateException("Aucune stratégie de plus court chemin définie");
        }
        return pathStrategy.findShortestPath(this, start, end);
    }
    
    public GraphNode findNodeAt(double x, double y) {
        for (GraphNode node : nodes) {
            if (node.contains(x, y)) {
                return node;
            }
        }
        return null;
    }
    
    public void clearHighlights() {
        for (GraphNode node : nodes) {
            node.setHighlighted(false);
        }
        for (GraphEdge edge : edges) {
            edge.setHighlighted(false);
        }
    }
    
    public void highlightPath(List<GraphNode> path) {
        clearHighlights();
        
        // Mettre en surbrillance les nœuds du chemin
        for (GraphNode node : path) {
            node.setHighlighted(true);
        }
        
        // Mettre en surbrillance les arêtes du chemin
        for (int i = 0; i < path.size() - 1; i++) {
            GraphNode current = path.get(i);
            GraphNode next = path.get(i + 1);
            
            for (GraphEdge edge : edges) {
                if ((edge.getStartNode().equals(current) && edge.getEndNode().equals(next)) ||
                    (edge.getStartNode().equals(next) && edge.getEndNode().equals(current))) {
                    edge.setHighlighted(true);
                    break;
                }
            }
        }
    }
    
    // Obtenir les voisins d'un nœud avec leurs poids
    public Map<GraphNode, Double> getNeighbors(GraphNode node) {
        Map<GraphNode, Double> neighbors = new HashMap<>();
        
        for (GraphEdge edge : edges) {
            if (edge.getStartNode().equals(node)) {
                neighbors.put(edge.getEndNode(), edge.getWeight());
            } else if (edge.getEndNode().equals(node)) {
                neighbors.put(edge.getStartNode(), edge.getWeight());
            }
        }
        
        return neighbors;
    }
    
    public void clear() {
        nodes.clear();
        edges.clear();
    }
    
    public boolean isEmpty() {
        return nodes.isEmpty() && edges.isEmpty();
    }
    
    public int getNodeCount() {
        return nodes.size();
    }
    
    public int getEdgeCount() {
        return edges.size();
    }
}
