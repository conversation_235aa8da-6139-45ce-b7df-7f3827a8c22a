# Application de Dessin JavaFX - Design Patterns

## Vue d'ensemble

Cette application JavaFX démontre l'utilisation de plusieurs design patterns pour créer une application de dessin modulaire et extensible. Elle permet de dessiner des formes géométriques, de les sauvegarder, et inclut une étude de cas pour les algorithmes de plus court chemin sur des graphes.

## Fonctionnalités Principales

### ✅ Fonctionnalités de Base
- **Sélection de formes** : Rectangle, Cercle, Ligne
- **Dessin interactif** : Clic pour créer des formes
- **Sauvegarde/Chargement** : Persistance en fichier ou base de données
- **Journalisation** : <PERSON><PERSON><PERSON>, fichi<PERSON>, ou base de données
- **Décoration** : Bordures épaisses pour les formes

### ✅ Étude de Cas - Graphes
- **Création de graphes** : Nœuds et arêtes interactifs
- **Algorithmes de plus court chemin** : Dijkstra et Bellman-Ford
- **Visualisation** : Mise en surbrillance des chemins
- **Interface dédiée** : Application spécialisée pour les graphes

## Design Patterns Implémentés

### 1. 🎯 Pattern Strategy

#### **A. Stratégies de Dessin**
- **Interface** : `DrawingStrategy`
- **Implémentations** : `RectangleDrawingStrategy`, `CircleDrawingStrategy`, `LineDrawingStrategy`
- **Utilisation** : Permet de changer le type de forme à dessiner à l'exécution

```java
// Exemple d'utilisation
DrawingStrategy strategy = new RectangleDrawingStrategy();
ShapeAdapter shape = strategy.draw(x, y);
```

#### **B. Stratégies de Journalisation**
- **Interface** : `LoggerStrategy`
- **Implémentations** : `ConsoleLogger`, `FileLogger`, `DBLogger`
- **Utilisation** : Changement du mode de logging à l'exécution

```java
// Exemple d'utilisation
LoggerSingleton.getInstance().setStrategy(new FileLogger());
LoggerSingleton.getInstance().log("Message");
```

#### **C. Stratégies d'Algorithmes de Plus Court Chemin**
- **Interface** : `ShortestPathStrategy`
- **Implémentations** : `DijkstraStrategy`, `BellmanFordStrategy`
- **Utilisation** : Choix de l'algorithme pour calculer le plus court chemin

```java
// Exemple d'utilisation
graph.setPathStrategy(new DijkstraStrategy());
List<GraphNode> path = graph.findShortestPath(start, end);
```

#### **D. Stratégies de Persistance**
- **Interface** : `PersistenceStrategy`
- **Implémentations** : `FilePersistenceStrategy`, `DatabasePersistenceStrategy`
- **Utilisation** : Choix du mode de sauvegarde

### 2. 🔄 Pattern Adapter

#### **A. Adapter de Formes**
- **Interface** : `ShapeAdapter`
- **Classe abstraite** : `Shape`
- **Implémentations** : `Rectangle`, `Circle`, `Line`
- **Utilisation** : Interface unifiée pour toutes les formes

#### **B. Adapter de Persistance**
- **Classe** : `ShapePersistenceAdapter`
- **Utilisation** : Adapte différentes stratégies de persistance

### 3. 🏗️ Pattern Decorator

#### **Décorateur de Bordures**
- **Classe abstraite** : `ShapeDecorator`
- **Implémentation** : `BorderDecorator`
- **Utilisation** : Ajoute des bordures épaisses aux formes

```java
// Exemple d'utilisation
ShapeAdapter decoratedShape = new BorderDecorator(originalShape);
```

### 4. 🎯 Pattern Singleton

#### **Singleton de Journalisation**
- **Classe** : `LoggerSingleton`
- **Utilisation** : Instance unique pour la journalisation globale

#### **Singleton du Tableau de Dessin**
- **Classe** : `DrawingBoard`
- **Utilisation** : Gestion centralisée de l'état du dessin

### 5. 👁️ Pattern Observer

#### **Observateur de Dessin**
- **Interface Subject** : `DrawingSubject`
- **Interface Observer** : `DrawingObserver`
- **Implémentation** : `DrawingBoard` (Subject), `LogObserver` (Observer)
- **Utilisation** : Notification automatique des changements

## Structure du Projet

```
src/designPattern_MiniProjet/
├── Applications/
│   ├── DrawingApplication.java      # Application principale de dessin
│   └── GraphApplication.java        # Application spécialisée pour les graphes
├── Shapes/
│   ├── ShapeAdapter.java           # Interface Adapter
│   ├── Shape.java                  # Classe abstraite de base
│   ├── Rectangle.java              # Forme Rectangle
│   ├── Circle.java                 # Forme Circle
│   └── Line.java                   # Forme Line
├── Strategies/
│   ├── DrawingStrategy.java        # Interface Strategy pour le dessin
│   ├── RectangleDrawingStrategy.java
│   ├── CircleDrawingStrategy.java
│   ├── LineDrawingStrategy.java
│   ├── LoggerStrategy.java         # Interface Strategy pour le logging
│   ├── ConsoleLogger.java
│   ├── FileLogger.java
│   └── DBLogger.java
├── Decorators/
│   ├── ShapeDecorator.java         # Décorateur abstrait
│   └── BorderDecorator.java        # Décorateur de bordures
├── Singletons/
│   ├── LoggerSingleton.java        # Singleton de logging
│   └── DrawingBoard.java           # Singleton + Observer
├── Observers/
│   ├── DrawingSubject.java         # Interface Subject
│   ├── DrawingObserver.java        # Interface Observer
│   └── LogObserver.java            # Observer concret
├── Persistence/
│   └── ShapePersistenceAdapter.java # Adapter de persistance
├── Graph/
│   ├── Graph.java                  # Classe principale du graphe
│   ├── GraphNode.java              # Nœud de graphe
│   ├── GraphEdge.java              # Arête de graphe
│   ├── ShortestPathStrategy.java   # Interface Strategy pour algorithmes
│   ├── DijkstraStrategy.java       # Algorithme de Dijkstra
│   └── BellmanFordStrategy.java    # Algorithme de Bellman-Ford
└── module-info.java                # Configuration du module
```

## Comment Exécuter

### Application Principale de Dessin
```bash
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.fxml designPattern_MiniProjet.DrawingApplication
```

### Application de Graphes
```bash
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.fxml designPattern_MiniProjet.GraphApplication
```

## Utilisation

### Application de Dessin
1. **Sélectionner une forme** : Rectangle, Cercle, ou Ligne
2. **Cliquer sur la zone de dessin** pour créer la forme
3. **Activer le décorateur** pour ajouter des bordures
4. **Changer la stratégie de logging** : Console, Fichier, ou Base de données
5. **Sauvegarder/Charger** le dessin

### Application de Graphes
1. **Mode création de nœuds** : Cliquer pour créer des nœuds
2. **Mode création d'arêtes** : Cliquer sur deux nœuds pour les connecter
3. **Mode sélection** : Sélectionner nœud de départ et d'arrivée
4. **Choisir l'algorithme** : Dijkstra ou Bellman-Ford
5. **Calculer le plus court chemin**

## Avantages des Design Patterns

### 🎯 **Strategy**
- **Flexibilité** : Changement d'algorithme à l'exécution
- **Extensibilité** : Ajout facile de nouvelles stratégies
- **Séparation des responsabilités** : Chaque algorithme dans sa propre classe

### 🔄 **Adapter**
- **Compatibilité** : Interface unifiée pour des classes différentes
- **Réutilisabilité** : Adaptation de code existant
- **Isolation** : Séparation entre interface et implémentation

### 🏗️ **Decorator**
- **Composition** : Ajout de fonctionnalités sans modification
- **Flexibilité** : Combinaison de décorateurs
- **Principe ouvert/fermé** : Ouvert à l'extension, fermé à la modification

### 🎯 **Singleton**
- **Contrôle d'accès** : Instance unique garantie
- **État global** : Partage d'état entre composants
- **Économie de ressources** : Une seule instance

### 👁️ **Observer**
- **Couplage faible** : Observateurs indépendants du sujet
- **Notification automatique** : Mise à jour en temps réel
- **Extensibilité** : Ajout facile de nouveaux observateurs

## Technologies Utilisées

- **Java 17** : Langage de programmation
- **JavaFX 21** : Interface graphique
- **MySQL** : Base de données (optionnel)
- **Design Patterns** : Architecture logicielle

## Explications Détaillées des Design Patterns

### 🎯 Pattern Strategy - Explication Approfondie

Le pattern Strategy permet de définir une famille d'algorithmes, de les encapsuler et de les rendre interchangeables. L'algorithme peut varier indépendamment des clients qui l'utilisent.

#### **Composants du Pattern Strategy :**

1. **Strategy (Interface)** : Définit l'interface commune à tous les algorithmes
2. **ConcreteStrategy** : Implémente l'algorithme en utilisant l'interface Strategy
3. **Context** : Maintient une référence vers un objet Strategy et délègue le travail

#### **Exemple dans notre projet :**

<augment_code_snippet path="src/designPattern_MiniProjet/DrawingStrategy.java" mode="EXCERPT">
````java
public interface DrawingStrategy {
    ShapeAdapter draw(double x, double y);
    void draw(GraphicsContext gc, ShapeAdapter shape);
}
````
</augment_code_snippet>

<augment_code_snippet path="src/designPattern_MiniProjet/RectangleDrawingStrategy.java" mode="EXCERPT">
````java
public class RectangleDrawingStrategy implements DrawingStrategy {
    @Override
    public ShapeAdapter draw(double x, double y) {
        return new Rectangle(x, y);
    }

    @Override
    public void draw(GraphicsContext gc, ShapeAdapter shape) {
        gc.setFill(Color.BLUE);
        gc.fillRect(shape.getX(), shape.getY(), shape.getWidth(), shape.getHeight());
    }
}
````
</augment_code_snippet>

#### **Avantages :**
- ✅ **Flexibilité** : Changement d'algorithme à l'exécution
- ✅ **Extensibilité** : Ajout facile de nouvelles stratégies
- ✅ **Principe Ouvert/Fermé** : Ouvert à l'extension, fermé à la modification
- ✅ **Élimination des conditionnelles** : Pas de switch/if pour choisir l'algorithme

### 🔄 Pattern Adapter - Explication Approfondie

Le pattern Adapter permet à des interfaces incompatibles de collaborer. Il agit comme un wrapper entre deux objets.

#### **Composants du Pattern Adapter :**

1. **Target** : Interface que le client attend
2. **Adapter** : Classe qui adapte l'Adaptee au Target
3. **Adaptee** : Classe existante avec une interface incompatible
4. **Client** : Collabore avec les objets conformes à l'interface Target

#### **Exemple dans notre projet :**

<augment_code_snippet path="src/designPattern_MiniProjet/ShapeAdapter.java" mode="EXCERPT">
````java
public interface ShapeAdapter {
    void setPosition(double x, double y);
    void draw(GraphicsContext gc);
    double getX();
    double getY();
    // ... autres méthodes
}
````
</augment_code_snippet>

<augment_code_snippet path="src/designPattern_MiniProjet/Shape.java" mode="EXCERPT">
````java
public abstract class Shape implements ShapeAdapter {
    protected double x, y;
    protected double width, height;
    protected int radius;

    @Override
    public void setPosition(double x, double y) {
        this.x = x;
        this.y = y;
    }
    // ... implémentation des méthodes de l'interface
}
````
</augment_code_snippet>

#### **Avantages :**
- ✅ **Réutilisabilité** : Utilisation de code existant
- ✅ **Séparation des responsabilités** : Logique métier séparée de l'interface
- ✅ **Compatibilité** : Interface unifiée pour des classes différentes

### 🏗️ Pattern Decorator - Explication Approfondie

Le pattern Decorator permet d'ajouter dynamiquement de nouvelles fonctionnalités à un objet sans altérer sa structure.

#### **Composants du Pattern Decorator :**

1. **Component** : Interface commune aux objets et décorateurs
2. **ConcreteComponent** : Objet auquel on peut ajouter des responsabilités
3. **Decorator** : Classe abstraite qui implémente Component et contient une référence vers Component
4. **ConcreteDecorator** : Ajoute des responsabilités au composant

#### **Exemple dans notre projet :**

<augment_code_snippet path="src/designPattern_MiniProjet/ShapeDecorator.java" mode="EXCERPT">
````java
public abstract class ShapeDecorator implements ShapeAdapter {
    protected ShapeAdapter decoratedShape;

    public ShapeDecorator(ShapeAdapter decoratedShape) {
        this.decoratedShape = decoratedShape;
    }

    public void draw(GraphicsContext gc) {
        decoratedShape.draw(gc);
    }
}
````
</augment_code_snippet>

<augment_code_snippet path="src/designPattern_MiniProjet/BorderDecorator.java" mode="EXCERPT">
````java
public class BorderDecorator extends ShapeDecorator {
    private Color borderColor;
    private double borderWidth;

    @Override
    public void draw(GraphicsContext gc) {
        super.draw(gc); // Dessiner la forme originale

        // Ajouter la bordure décorative
        gc.setStroke(borderColor);
        gc.setLineWidth(borderWidth);
        // ... logique de dessin de bordure
    }
}
````
</augment_code_snippet>

#### **Avantages :**
- ✅ **Composition** : Alternative flexible à l'héritage
- ✅ **Responsabilités dynamiques** : Ajout/suppression à l'exécution
- ✅ **Combinaison** : Plusieurs décorateurs peuvent être combinés

### 🎯 Pattern Singleton - Explication Approfondie

Le pattern Singleton garantit qu'une classe n'a qu'une seule instance et fournit un point d'accès global à cette instance.

#### **Composants du Pattern Singleton :**

1. **Instance unique** : Variable statique privée
2. **Constructeur privé** : Empêche l'instanciation externe
3. **Méthode getInstance()** : Point d'accès global à l'instance

#### **Exemple dans notre projet :**

<augment_code_snippet path="src/designPattern_MiniProjet/LoggerSingleton.java" mode="EXCERPT">
````java
public class LoggerSingleton {
    private static LoggerSingleton instance;
    private LoggerStrategy strategy;

    private LoggerSingleton() {} // Constructeur privé

    public static LoggerSingleton getInstance() {
        if (instance == null) {
            instance = new LoggerSingleton();
        }
        return instance;
    }
}
````
</augment_code_snippet>

#### **Avantages :**
- ✅ **Contrôle d'accès** : Instance unique garantie
- ✅ **Économie de ressources** : Une seule instance
- ✅ **Point d'accès global** : Accessible depuis n'importe où

### 👁️ Pattern Observer - Explication Approfondie

Le pattern Observer définit une dépendance un-à-plusieurs entre objets, de sorte que lorsqu'un objet change d'état, tous ses dépendants sont notifiés automatiquement.

#### **Composants du Pattern Observer :**

1. **Subject** : Interface pour attacher/détacher des observateurs
2. **ConcreteSubject** : Stocke l'état et notifie les observateurs
3. **Observer** : Interface pour les objets qui doivent être notifiés
4. **ConcreteObserver** : Implémente l'interface Observer

#### **Exemple dans notre projet :**

<augment_code_snippet path="src/designPattern_MiniProjet/DrawingSubject.java" mode="EXCERPT">
````java
public interface DrawingSubject {
    void attach(DrawingObserver observer);
    void detach(DrawingObserver observer);
    void notifyObservers();
}
````
</augment_code_snippet>

<augment_code_snippet path="src/designPattern_MiniProjet/DrawingBoard.java" mode="EXCERPT">
````java
public class DrawingBoard implements DrawingSubject {
    private List<DrawingObserver> observers = new ArrayList<>();

    @Override
    public void notifyObservers() {
        for (DrawingObserver observer : observers) {
            observer.update();
        }
    }
}
````
</augment_code_snippet>

#### **Avantages :**
- ✅ **Couplage faible** : Subject et Observer sont faiblement couplés
- ✅ **Communication dynamique** : Ajout/suppression d'observateurs à l'exécution
- ✅ **Principe de responsabilité unique** : Séparation des préoccupations

## Tests et Validation

### Compilation
```bash
javac --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.graphics -d bin src/designPattern_MiniProjet/*.java src/module-info.java
```

### Exécution
```bash
# Application principale
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.graphics -cp bin designPattern_MiniProjet.DrawingApplication

# Application graphe
java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.graphics -cp bin designPattern_MiniProjet.GraphApplication
```

## Conclusion

Ce projet démontre l'utilisation efficace de 5 design patterns majeurs dans une application JavaFX réelle. Chaque pattern apporte ses propres avantages :

- **Strategy** : Flexibilité des algorithmes
- **Adapter** : Compatibilité des interfaces
- **Decorator** : Extension dynamique des fonctionnalités
- **Singleton** : Contrôle d'instance unique
- **Observer** : Communication automatique entre objets

L'architecture résultante est modulaire, extensible et maintenable, démontrant la puissance des design patterns dans le développement logiciel.

## Auteur

Projet réalisé dans le cadre de l'étude des design patterns en Java.
