package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Représente une connexion entre deux formes dans le graphe unifié
 * Les formes servent de nœuds et les connexions d'arêtes
 */
public class ShapeConnection {
    private Shape fromShape;
    private Shape toShape;
    private double weight;
    private boolean highlighted = false;
    
    public ShapeConnection(Shape fromShape, Shape toShape) {
        this.fromShape = fromShape;
        this.toShape = toShape;
        this.weight = calculateDistance();
    }
    
    private double calculateDistance() {
        double dx = fromShape.getCenterX() - toShape.getCenterX();
        double dy = fromShape.getCenterY() - toShape.getCenterY();
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    public void draw(GraphicsContext gc) {
        Color connectionColor = highlighted ? Color.RED : Color.BLACK;
        double lineWidth = highlighted ? 3.0 : 1.0;
        
        gc.setStroke(connectionColor);
        gc.setLineWidth(lineWidth);
        gc.strokeLine(
            fromShape.getCenterX(), fromShape.getCenterY(),
            toShape.getCenterX(), toShape.getCenterY()
        );
        
        // Dessiner le poids de la connexion
        double midX = (fromShape.getCenterX() + toShape.getCenterX()) / 2;
        double midY = (fromShape.getCenterY() + toShape.getCenterY()) / 2;
        
        gc.setFill(Color.BLACK);
        gc.fillText(String.format("%.1f", weight), midX, midY);
    }
    
    public Shape getFromShape() {
        return fromShape;
    }
    
    public Shape getToShape() {
        return toShape;
    }
    
    public double getWeight() {
        return weight;
    }
    
    public boolean isHighlighted() {
        return highlighted;
    }
    
    public void setHighlighted(boolean highlighted) {
        this.highlighted = highlighted;
    }
    
    public boolean connects(Shape shape1, Shape shape2) {
        return (fromShape.equals(shape1) && toShape.equals(shape2)) ||
               (fromShape.equals(shape2) && toShape.equals(shape1));
    }
}
