package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe abstraite de base pour toutes les formes géométriques
 * Utilise le pattern Strategy pour déléguer le dessin
 */
public abstract class Shape implements Drawable {
    protected double x, y;
    protected double width, height;
    protected Color color;
    protected DrawingStrategy drawingStrategy;
    
    // Propriétés pour fonctionner comme nœud de graphe
    protected String nodeId;
    protected boolean selected = false;
    protected boolean highlighted = false;
    
    public Shape(double x, double y, double width, double height, Color color) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
        this.nodeId = generateNodeId();
    }
    
    // Génère un ID unique pour le nœud basé sur la position et le type
    protected String generateNodeId() {
        return getClass().getSimpleName().substring(0, 1) + 
               String.format("%.0f", x) + "_" + String.format("%.0f", y);
    }
    
    public void setPosition(double x, double y) {
        this.x = x;
        this.y = y;
    }
    
    public double getX() {
        return x;
    }
    
    public double getY() {
        return y;
    }
    
    public double getWidth() {
        return width;
    }
    
    public double getHeight() {
        return height;
    }
    
    public Color getColor() {
        return color;
    }
    
    public void setColor(Color color) {
        this.color = color;
    }
    
    // Méthodes pour le graphe
    public String getNodeId() {
        return nodeId;
    }
    
    public boolean isSelected() {
        return selected;
    }
    
    public void setSelected(boolean selected) {
        this.selected = selected;
    }
    
    public boolean isHighlighted() {
        return highlighted;
    }
    
    public void setHighlighted(boolean highlighted) {
        this.highlighted = highlighted;
    }
    
    // Vérifie si un point est dans cette forme (pour la sélection)
    public boolean contains(double px, double py) {
        return px >= x && px <= x + width && py >= y && py <= y + height;
    }
    
    // Centre de la forme pour les connexions
    public double getCenterX() {
        return x + width / 2;
    }
    
    public double getCenterY() {
        return y + height / 2;
    }
    
    @Override
    public void draw(GraphicsContext gc) {
        if (drawingStrategy != null) {
            drawingStrategy.draw(gc, this);
        }
    }
    
    // Méthode pour la sérialisation
    public abstract String serialize();
    
    // Factory method pour la désérialisation
    public static Shape deserialize(String data) {
        String[] parts = data.split(",");
        if (parts.length < 1) {
            throw new IllegalArgumentException("Invalid shape data");
        }
        
        String type = parts[0];
        switch (type) {
            case "CIRCLE":
                return Circle.deserialize(data);
            case "RECTANGLE":
                return Rectangle.deserialize(data);
            case "LINE":
                return Line.deserialize(data);
            default:
                throw new IllegalArgumentException("Unknown shape type: " + type);
        }
    }
}
