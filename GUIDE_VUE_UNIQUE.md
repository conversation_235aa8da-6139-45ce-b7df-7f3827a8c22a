# 🎨 Guide d'Utilisation - Application de Dessin Vue Unique

## 📋 Vue d'Ensemble

L'**Application de Dessin Vue Unique** combine maintenant toutes les fonctionnalités en une seule interface sans onglets :
- **Dessin de formes géométriques** (cercles, rectangles, lignes)
- **Création et analyse de graphes** avec calcul du plus court chemin
- **Journalisation configurable** (console, fichier, base de données)
- **Sauvegarde/Ouverture** des dessins

## 🚀 Lancement de l'Application

### Option 1: <PERSON><PERSON><PERSON> (Recommandé)
```bash
run_single_view_app.bat
```

### Option 2: Ligne de commande
```bash
cd "c:\Users\<USER>\eclipse-workspace\designPattern_MiniProjet"
java --module-path "C:\Program Files\Java\javafx-sdk-21.0.1\lib" --add-modules javafx.controls,javafx.fxml -cp "bin" designPattern_MiniProjet.UnifiedSingleViewApplication
```

## 🎯 Fonctionnalités de l'Interface Unifiée

### 1. **Section Formes Géométriques** 🎨

#### Outils de Formes:
- **⭕ Cercle** : Sélectionne l'outil cercle
- **⬛ Rectangle** : Sélectionne l'outil rectangle  
- **➖ Ligne** : Sélectionne l'outil ligne

#### Sélection des Couleurs:
- **Sélecteur de couleur** personnalisé
- **Couleurs rapides** : 🔴 Rouge, 🔵 Bleu, 🟢 Vert, 🟡 Jaune

#### Utilisation:
1. Sélectionnez une forme (cercle, rectangle, ligne)
2. Choisissez une couleur
3. Cliquez sur la zone de dessin pour créer la forme

### 2. **Section Outils de Graphe** 🔗

#### Modes de Création:
- **➕ Créer Nœuds** : Mode création de nœuds
- **🔗 Créer Arêtes** : Mode création d'arêtes entre nœuds
- **👆 Sélectionner** : Mode sélection pour algorithmes

#### Algorithmes Disponibles:
- **Dijkstra** : Algorithme classique pour graphes avec poids positifs
- **Bellman-Ford** : Algorithme pour graphes avec poids négatifs

#### Workflow:
1. Créez des nœuds en cliquant sur la zone (mode ➕ Créer Nœuds)
2. Connectez-les avec des arêtes (mode 🔗 Créer Arêtes)
3. Sélectionnez les nœuds de départ et d'arrivée (mode 👆 Sélectionner)
4. Cliquez "🧮 Calculer Chemin" pour voir le plus court chemin

### 3. **Section Actions & Configuration** ⚙️

#### Actions de Fichier:
- **💾 Sauvegarder** : Sauvegarde le dessin actuel (formes uniquement)
- **📂 Ouvrir** : Charge un dessin existant
- **🗑️ Effacer Tout** : Efface toutes les formes ET tous les graphes

#### Journalisation:
- **Console** : Messages dans la console
- **Fichier** : Enregistrement dans un fichier de log
- **Base de données** : Stockage en base de données

#### Actions de Graphe:
- **🧹 Effacer Chemin** : Efface uniquement le chemin calculé

## 🏗️ Architecture et Design Patterns

### Patterns Implémentés:

1. **Singleton Pattern**:
   - `LoggerSingleton` : Instance unique du logger
   - `DrawingBoard` : Instance unique du tableau de dessin

2. **Strategy Pattern**:
   - `LoggerStrategy` : Différentes stratégies de journalisation
   - `DrawingStrategy` : Stratégies de dessin pour chaque forme
   - `ShortestPathStrategy` : Algorithmes de plus court chemin
   - `PersistenceStrategy` : Stratégies de sauvegarde

3. **Observer Pattern**:
   - `DrawingObserver` : Notifications des changements de dessin

4. **Factory Pattern**:
   - `ShapeFactory` : Création des formes géométriques

5. **Adapter Pattern**:
   - `ShapePersistenceAdapter` : Adaptation pour la persistance

6. **Decorator Pattern**:
   - `ShapeDecorator` : Décoration des formes (utilisé en interne)

## 🔧 Fonctionnalités Avancées

### Interface Unifiée:
- **Une seule vue** pour toutes les fonctionnalités
- **Barre d'état** avec informations en temps réel
- **Interface intuitive** avec sections organisées par couleur

### Utilisation Mixte:
- **Formes et graphes simultanés** : Vous pouvez dessiner des formes ET créer des graphes dans la même zone
- **Effacement sélectif** : Effacez tout ou juste les chemins
- **Journalisation unifiée** : Toutes les actions sont loggées

### Persistance:
- **Sauvegarde des formes** uniquement (les graphes ne sont pas sauvegardés)
- **Format personnalisé** `.drawing`
- **Rechargement avec reconstruction** des objets

## 📊 Avantages de la Vue Unique

### Avant (application avec onglets):
- ❌ Navigation nécessaire entre modes
- ❌ Impossible de voir formes et graphes simultanément
- ❌ Interface fragmentée

### Maintenant (vue unique):
- ✅ **Tout accessible** en une seule vue
- ✅ **Formes et graphes ensemble** dans la même zone
- ✅ **Interface cohérente** et simplifiée
- ✅ **Workflow fluide** sans basculement de modes
- ✅ **Outils organisés** par sections colorées

## 🎯 Cas d'Usage

### Scénario 1: Création Artistique et Analytique
1. Lancez l'application
2. Dessinez des formes décoratives
3. Ajoutez un graphe par-dessus
4. Analysez les chemins tout en gardant le contexte visuel

### Scénario 2: Visualisation de Données
1. Créez un arrière-plan avec des formes
2. Superposez un réseau de nœuds
3. Calculez les plus courts chemins
4. Le tout dans une vue cohérente

### Scénario 3: Apprentissage Interactif
1. Utilisez les formes pour délimiter des zones
2. Créez des réseaux dans ces zones
3. Testez différents algorithmes
4. Observez les résultats en contexte

## 🔍 Conseils d'Utilisation

### Productivité:
- Activez la journalisation fichier pour suivre vos actions
- Utilisez les couleurs rapides pour changer rapidement de style
- Sauvegardez régulièrement vos créations

### Workflow Optimal:
1. Commencez par dessiner un contexte avec les formes
2. Ajoutez les nœuds aux positions stratégiques
3. Connectez avec des arêtes
4. Testez les algorithmes de chemin

### Débogage:
- Consultez les logs pour comprendre les actions
- Utilisez "Effacer Chemin" pour nettoyer sans perdre le graphe
- La barre d'état donne des informations en temps réel

---

Cette application vue unique représente maintenant la **solution optimale** selon vos spécifications, intégrant tous les design patterns requis dans une interface moderne, cohérente et sans fragmentation ! 🚀
