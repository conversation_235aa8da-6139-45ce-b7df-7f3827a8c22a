@echo off
echo ========================================
echo   Application de Dessin Unifiee Vue Unique
echo ========================================
echo.
echo Demarrage de l'application JavaFX...
echo.

cd /d "c:\Users\<USER>\eclipse-workspace\designPattern_MiniProjet"

REM Compiler l'application si necessaire
echo Compilation en cours...
javac --module-path "C:\Users\<USER>\Downloads\openjfx-21.0.7_windows-x64_bin-sdk\javafx-sdk-21.0.7\lib" --add-modules javafx.controls,javafx.fxml -cp "src" -d "bin" src\designPattern_MiniProjet\UnifiedSingleViewApplication.java

if %errorlevel% neq 0 (
    echo ERREUR: Compilation echouee
    pause
    exit /b 1
)

REM Essayer plusieurs configurations JavaFX
echo Lancement de l'application...

REM Essai 1: JavaFX correct du projet
java --module-path "C:\Users\<USER>\Downloads\openjfx-21.0.7_windows-x64_bin-sdk\javafx-sdk-21.0.7\lib" --add-modules javafx.controls,javafx.fxml -cp "bin" designPattern_MiniProjet.UnifiedSingleViewApplication

if %errorlevel% neq 0 (
    echo Essai configuration alternative...
    REM Essai 2: JavaFX SDK alternatif
    java --module-path "C:\Program Files\Java\javafx-sdk-21.0.1\lib" --add-modules javafx.controls,javafx.fxml -cp "bin" designPattern_MiniProjet.UnifiedSingleViewApplication
)

if %errorlevel% neq 0 (
    echo Essai sans modules...
    REM Essai 3: Sans système de modules (JavaFX dans classpath)
    java -cp "bin" designPattern_MiniProjet.UnifiedSingleViewApplication
)

echo.
echo Application fermee.
pause
