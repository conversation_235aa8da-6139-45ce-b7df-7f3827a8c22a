package designPattern_MiniProjet;

/**
 * Singleton Logger thread-safe qui utilise le pattern Strategy
 * Version corrigée avec double-checked locking
 */
public class LoggerSingleton {
    private static volatile LoggerSingleton instance;
    private LoggerStrategy strategy;

    private LoggerSingleton() {
        // Stratégie par défaut
        this.strategy = new ConsoleLogger();
    }

    public static LoggerSingleton getInstance() {
        if (instance == null) {
            synchronized (LoggerSingleton.class) {
                if (instance == null) {
                    instance = new LoggerSingleton();
                }
            }
        }
        return instance;
    }

    public synchronized void setStrategy(LoggerStrategy strategy) {
        if (strategy != null) {
            this.strategy = strategy;
        }
    }

    public void log(String message) {
        if (strategy != null) {
            strategy.log(message);
        }
    }
}
