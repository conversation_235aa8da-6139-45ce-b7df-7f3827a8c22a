package designPattern_MiniProjet;

import designPattern_MiniProjet.graph.GraphNode;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe représentant une arête de graphe
 * Hérite de Line pour la représentation visuelle
 */
public class GraphEdge extends Line {
    private GraphNode startNode;
    private GraphNode endNode;
    private double weight;
    private boolean isHighlighted = false;
    
    public GraphEdge(GraphNode startNode, GraphNode endNode, double weight) {
        super(startNode.getX() + startNode.getRadius(), 
              startNode.getY() + startNode.getRadius(),
              endNode.getX() + endNode.getRadius(), 
              endNode.getY() + endNode.getRadius());
        this.startNode = startNode;
        this.endNode = endNode;
        this.weight = weight;
    }
    
    public GraphEdge(GraphNode startNode, GraphNode endNode) {
        this(startNode, endNode, calculateDistance(startNode, endNode));
    }
    
    private static double calculateDistance(GraphNode node1, GraphNode node2) {
        double dx = (node1.getX() + node1.getRadius()) - (node2.getX() + node2.getRadius());
        double dy = (node1.getY() + node1.getRadius()) - (node2.getY() + node2.getRadius());
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    public GraphNode getStartNode() {
        return startNode;
    }
    
    public GraphNode getEndNode() {
        return endNode;
    }
    
    public double getWeight() {
        return weight;
    }
    
    public void setWeight(double weight) {
        this.weight = weight;
    }
    
    public boolean isHighlighted() {
        return isHighlighted;
    }
    
    public void setHighlighted(boolean highlighted) {
        this.isHighlighted = highlighted;
    }
    
    @Override
    public void draw(GraphicsContext gc) {
        // Couleur selon l'état
        if (isHighlighted) {
            gc.setStroke(Color.RED);
            gc.setLineWidth(4);
        } else {
            gc.setStroke(Color.BLACK);
            gc.setLineWidth(2);
        }
        
        // Dessiner la ligne
        gc.strokeLine(x, y, getEndX(), getEndY());
        
        // Dessiner le poids au milieu de l'arête
        double midX = (x + getEndX()) / 2;
        double midY = (y + getEndY()) / 2;
        
        gc.setFill(Color.WHITE);
        gc.fillOval(midX - 15, midY - 10, 30, 20);
        gc.setStroke(Color.BLACK);
        gc.setLineWidth(1);
        gc.strokeOval(midX - 15, midY - 10, 30, 20);
        
        gc.setFill(Color.BLACK);
        gc.fillText(String.format("%.1f", weight), midX - 10, midY + 5);
    }
    
    @Override
    public String serialize() {
        return String.format("GRAPHEDGE,%.2f,%.2f,%.2f,%.2f,%.2f", 
                           x, y, getEndX(), getEndY(), weight);
    }
    
    @Override
    public String getType() {
        return "GraphEdge";
    }
    
    // Factory method pour créer depuis une chaîne sérialisée
    public static GraphEdge deserialize(String data) {
        String[] parts = data.split(",");
        if (parts.length >= 6 && "GRAPHEDGE".equals(parts[0])) {
            double startX = Double.parseDouble(parts[1]);
            double startY = Double.parseDouble(parts[2]);
            double endX = Double.parseDouble(parts[3]);
            double endY = Double.parseDouble(parts[4]);
            double weight = Double.parseDouble(parts[5]);
            
            // Créer des nœuds temporaires pour l'arête
            GraphNode startNode = new GraphNode(startX - 25, startY - 25);
            GraphNode endNode = new GraphNode(endX - 25, endY - 25);
            
            return new GraphEdge(startNode, endNode, weight);
        }
        throw new IllegalArgumentException("Invalid graph edge data: " + data);
    }
}
