package designPattern_MiniProjet;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Stage;

import java.util.ArrayList;
import java.util.List;

/**
 * Application principale JavaFX pour le dessin de formes géométriques
 * Utilise plusieurs design patterns : Strategy, Observer, Singleton, Decorator
 */
public class DrawingApplication extends Application {    private DrawingStrategy currentDrawingStrategy;
    private List<Shape> shapes = new ArrayList<>();
    private Canvas canvas;
    private GraphicsContext gc;
    private boolean decoratorMode = false;

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Application de Dessin - Design Patterns");

        // Initialiser le logger avec une stratégie par défaut
        LoggerSingleton.getInstance().setStrategy(new ConsoleLogger());

        // Interface utilisateur
        VBox root = createUI();        // Observateur pour rafraîchir la vue après chargement
        DrawingBoard.getInstance().addObserver((message) -> {
            shapes = DrawingBoard.getInstance().getShapes();
            redrawCanvas();
        });

        Scene scene = new Scene(root, 1000, 700);
        primaryStage.setScene(scene);
        primaryStage.show();

        LoggerSingleton.getInstance().log("Application démarrée");
    }

    private VBox createUI() {
        VBox root = new VBox(10);
        root.setPadding(new Insets(10));

        // Barre d'outils pour les formes
        HBox shapeToolbar = createShapeToolbar();

        // Barre d'outils pour les actions
        HBox actionToolbar = createActionToolbar();

        // Barre d'outils pour les options
        HBox optionToolbar = createOptionToolbar();

        // Zone de dessin
        canvas = new Canvas(950, 500);
        gc = canvas.getGraphicsContext2D();        // Gestion du clic dans la zone de dessin
        canvas.setOnMouseClicked(e -> {
            if (currentDrawingStrategy != null) {
                // Créer la forme avec la Factory
                Shape shape = null;
                if (currentDrawingStrategy instanceof CircleDrawingStrategy) {
                    shape = ShapeFactory.createShape("circle", e.getX() - 25, e.getY() - 25, Color.BLUE);
                } else if (currentDrawingStrategy instanceof RectangleDrawingStrategy) {
                    shape = ShapeFactory.createShape("rectangle", e.getX() - 25, e.getY() - 25, Color.RED);
                } else if (currentDrawingStrategy instanceof LineDrawingStrategy) {
                    shape = ShapeFactory.createShape("line", e.getX() - 25, e.getY() - 25, Color.GREEN);
                }

                if (shape != null) {
                    // Appliquer le décorateur si activé
                    if (decoratorMode) {
                        shape = new BorderDecorator(shape);
                    }

                    shapes.add(shape);
                    DrawingBoard.getInstance().addShape(shape);
                    
                    // Dessiner la forme en utilisant la stratégie
                    currentDrawingStrategy.draw(gc, shape);
                    
                    LoggerSingleton.getInstance().log("Forme dessinée à (" +
                            String.format("%.1f", e.getX()) + ", " +
                            String.format("%.1f", e.getY()) + ")");
                }
            }
        });

        root.getChildren().addAll(shapeToolbar, actionToolbar, optionToolbar, canvas);
        return root;
    }

    private HBox createShapeToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #f0f0f0;");

        Label shapeLabel = new Label("Formes:");
        shapeLabel.setStyle("-fx-font-weight: bold;");

        Button rectangleButton = new Button("Rectangle");
        Button circleButton = new Button("Cercle");
        Button lineButton = new Button("Ligne");

        rectangleButton.setOnAction(e -> {
            currentDrawingStrategy = new RectangleDrawingStrategy();
            LoggerSingleton.getInstance().log("Rectangle sélectionné");
        });

        circleButton.setOnAction(e -> {
            currentDrawingStrategy = new CircleDrawingStrategy();
            LoggerSingleton.getInstance().log("Cercle sélectionné");
        });

        lineButton.setOnAction(e -> {
            currentDrawingStrategy = new LineDrawingStrategy();
            LoggerSingleton.getInstance().log("Ligne sélectionnée");
        });

        toolbar.getChildren().addAll(shapeLabel, rectangleButton, circleButton, lineButton);
        return toolbar;
    }

    private HBox createActionToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #e0e0e0;");

        Label actionLabel = new Label("Actions:");
        actionLabel.setStyle("-fx-font-weight: bold;");

        Button saveButton = new Button("Enregistrer");
        Button loadButton = new Button("Charger");
        Button clearButton = new Button("Effacer");        saveButton.setOnAction(e -> {
            ShapePersistenceAdapter persistence = new ShapePersistenceAdapter();
            persistence.saveShapes(shapes, "drawing.dat");
            LoggerSingleton.getInstance().log("Dessin enregistré");
        });

        loadButton.setOnAction(e -> {
            ShapePersistenceAdapter persistence = new ShapePersistenceAdapter();
            shapes = persistence.loadShapes("drawing.dat");
            DrawingBoard.getInstance().setShapes(shapes);
            redrawCanvas();
            LoggerSingleton.getInstance().log("Dessin chargé");
        });

        clearButton.setOnAction(e -> {
            shapes.clear();
            DrawingBoard.getInstance().clearShapes();
            gc.clearRect(0, 0, canvas.getWidth(), canvas.getHeight());
            LoggerSingleton.getInstance().log("Zone de dessin effacée");
        });

        toolbar.getChildren().addAll(actionLabel, saveButton, loadButton, clearButton);
        return toolbar;
    }

    private HBox createOptionToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setPadding(new Insets(5));
        toolbar.setStyle("-fx-background-color: #d0d0d0;");

        // Options de décoration
        Label decoratorLabel = new Label("Décorateur:");
        decoratorLabel.setStyle("-fx-font-weight: bold;");

        CheckBox decoratorCheckBox = new CheckBox("Bordure épaisse");
        decoratorCheckBox.setOnAction(e -> {
            decoratorMode = decoratorCheckBox.isSelected();
            LoggerSingleton.getInstance().log("Mode décorateur: " +
                    (decoratorMode ? "activé" : "désactivé"));
        });

        // Options de logging
        Label logLabel = new Label("Journalisation:");
        logLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> logComboBox = new ComboBox<>();
        logComboBox.getItems().addAll("Console", "Fichier", "Base de données");
        logComboBox.setValue("Console");

        logComboBox.setOnAction(e -> {
            String selected = logComboBox.getValue();
            switch (selected) {
                case "Console":
                    LoggerSingleton.getInstance().setStrategy(new ConsoleLogger());
                    break;
                case "Fichier":
                    LoggerSingleton.getInstance().setStrategy(new FileLogger());
                    break;
                case "Base de données":
                    LoggerSingleton.getInstance().setStrategy(new DBLogger());
                    break;
            }
            LoggerSingleton.getInstance().log("Stratégie de logging changée: " + selected);
        });

        toolbar.getChildren().addAll(decoratorLabel, decoratorCheckBox,
                new Separator(), logLabel, logComboBox);
        return toolbar;
    }    private void redrawCanvas() {
        gc.clearRect(0, 0, canvas.getWidth(), canvas.getHeight());
        for (Shape shape : shapes) {
            // Utiliser la stratégie de dessin appropriée pour chaque forme
            DrawingStrategy strategy = getStrategyForShape(shape);
            if (strategy != null) {
                strategy.draw(gc, shape);
            }
        }
    }
    
    private DrawingStrategy getStrategyForShape(Shape shape) {
        if (shape instanceof Circle) {
            return new CircleDrawingStrategy();
        } else if (shape instanceof Rectangle) {
            return new RectangleDrawingStrategy();
        } else if (shape instanceof Line) {
            return new LineDrawingStrategy();
        }
        return null;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
