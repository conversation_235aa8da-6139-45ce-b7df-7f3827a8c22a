package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;

/**
 * Strategy concrète pour dessiner des lignes
 * Version corrigée qui utilise le polymorphisme
 */
public class LineDrawingStrategy implements DrawingStrategy {
    
    @Override
    public void draw(GraphicsContext gc, Shape shape) {
        if (shape instanceof Line) {
            Line line = (Line) shape;
            gc.setStroke(shape.getColor());
            gc.setLineWidth(3);
            gc.strokeLine(shape.getX(), shape.getY(), line.getEndX(), line.getEndY());
        }
    }
}
