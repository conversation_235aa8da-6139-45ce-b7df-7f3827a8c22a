package designPattern_MiniProjet;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Application JavaFX vraiment unifiée où les formes dessinées servent de nœuds
 * pour le graphe
 * Plus de séparation artificielle - les formes sont les nœuds, on peut les
 * connecter et calculer les plus courts chemins
 * Implémente tous les design patterns requis dans une interface cohérente
 */
public class UnifiedSingleViewApplication extends Application {

    private Canvas canvas;
    private GraphicsContext gc;
    private DrawingBoard drawingBoard;
    private List<ShapeConnection> connections = new ArrayList<>();

    // État de l'application
    private String currentTool = "CIRCLE"; // CIRCLE, RECTANGLE, LINE, CONNECT, SELECT

    // Paramètres de dessin
    private Color selectedColor = Color.BLUE;

    // Paramètres pour les connexions et calcul de chemin
    private Shape selectedShape1 = null;
    private Shape selectedShape2 = null;
    private Shape startPathShape = null;
    private Shape endPathShape = null;
    private ShortestPathStrategy pathStrategy = new DijkstraStrategy();

    // Interface utilisateur
    private VBox root;
    private Label statusLabel;

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Application Unifiée - Formes comme Nœuds de Graphe");

        // Initialiser les composants
        initializeComponents();

        // Créer l'interface utilisateur
        createUserInterface();

        Scene scene = new Scene(root, 1400, 900);
        primaryStage.setScene(scene);
        primaryStage.show();

        LoggerSingleton.getInstance().log("Application Unifiée (Formes = Nœuds) démarrée");
    }

    private void initializeComponents() {
        // Initialiser le logger par défaut
        LoggerSingleton.getInstance().setStrategy(new ConsoleLogger());

        // Initialiser le DrawingBoard (Singleton + Observer)
        drawingBoard = DrawingBoard.getInstance();

        // Observer pour logger les actions
        drawingBoard.addObserver(message -> LoggerSingleton.getInstance().log("DrawingBoard: " + message));
    }

    private void createUserInterface() {
        root = new VBox(10);
        root.setPadding(new Insets(10));

        // Barre d'outils principale unifiée
        VBox toolPanel = createUnifiedToolPanel();

        // Zone de dessin
        canvas = new Canvas(1350, 600);
        gc = canvas.getGraphicsContext2D();
        clearCanvas();

        // Gestionnaire de clics sur le canvas
        canvas.setOnMouseClicked(this::handleCanvasClick);

        // Barre d'état
        statusLabel = new Label("Application Unifiée - Les formes dessinées sont les nœuds du graphe");
        statusLabel.setStyle("-fx-font-size: 12px; -fx-padding: 5px; -fx-background-color: #e8f4f8;");

        root.getChildren().addAll(toolPanel, canvas, statusLabel);
    }

    private VBox createUnifiedToolPanel() {
        VBox panel = new VBox(15);
        panel.setPadding(new Insets(10));
        panel.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        // Titre
        Label titleLabel = new Label("Boite a Outils Unifiee - Formes = Noeuds");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        // Section 1: Création de Formes (qui deviennent des nœuds)
        VBox shapesSection = createShapesSection();

        // Section 2: Connexions et Graphe
        VBox graphSection = createGraphSection();

        // Section 3: Actions et Configuration
        VBox actionsSection = createActionsSection();

        panel.getChildren().addAll(titleLabel, new Separator(), shapesSection, new Separator(),
                graphSection, new Separator(), actionsSection);
        return panel;
    }

    private VBox createShapesSection() {
        VBox section = new VBox(10);

        Label sectionTitle = new Label("1. Creer des Formes (Noeuds du Graphe)");
        sectionTitle.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #007bff;");

        // Outils de forme
        HBox shapeTools = new HBox(10);
        ToggleGroup shapeGroup = new ToggleGroup();

        RadioButton circleBtn = new RadioButton("Cercle");
        RadioButton rectBtn = new RadioButton("Rectangle");
        RadioButton lineBtn = new RadioButton("Ligne");

        circleBtn.setToggleGroup(shapeGroup);
        rectBtn.setToggleGroup(shapeGroup);
        lineBtn.setToggleGroup(shapeGroup);
        circleBtn.setSelected(true);

        circleBtn.setOnAction(e -> {
            currentTool = "CIRCLE";
            updateStatus("Outil Cercle - Cliquez pour créer un nœud circulaire");
        });
        rectBtn.setOnAction(e -> {
            currentTool = "RECTANGLE";
            updateStatus("Outil Rectangle - Cliquez pour créer un nœud rectangulaire");
        });
        lineBtn.setOnAction(e -> {
            currentTool = "LINE";
            updateStatus("Outil Ligne - Cliquez pour créer un nœud linéaire");
        });

        shapeTools.getChildren().addAll(circleBtn, rectBtn, lineBtn);

        // Palette de couleurs
        HBox colorTools = new HBox(10);
        Label colorLabel = new Label("Couleur:");
        colorLabel.setStyle("-fx-font-weight: bold;");

        ColorPicker colorPicker = new ColorPicker(selectedColor);
        colorPicker.setOnAction(e -> selectedColor = colorPicker.getValue());

        Button redBtn = new Button("Rouge");
        Button blueBtn = new Button("Bleu");
        Button greenBtn = new Button("Vert");
        Button yellowBtn = new Button("Jaune");

        redBtn.setOnAction(e -> {
            selectedColor = Color.RED;
            colorPicker.setValue(Color.RED);
        });
        blueBtn.setOnAction(e -> {
            selectedColor = Color.BLUE;
            colorPicker.setValue(Color.BLUE);
        });
        greenBtn.setOnAction(e -> {
            selectedColor = Color.GREEN;
            colorPicker.setValue(Color.GREEN);
        });
        yellowBtn.setOnAction(e -> {
            selectedColor = Color.YELLOW;
            colorPicker.setValue(Color.YELLOW);
        });

        colorTools.getChildren().addAll(colorLabel, colorPicker, redBtn, blueBtn, greenBtn, yellowBtn);

        section.getChildren().addAll(sectionTitle, shapeTools, colorTools);
        return section;
    }

    private VBox createGraphSection() {
        VBox section = new VBox(10);

        Label sectionTitle = new Label("2. Connecter les Formes et Calculer les Chemins");
        sectionTitle.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #28a745;");

        // Outils de connexion
        HBox connectionTools = new HBox(10);
        ToggleGroup connectionGroup = new ToggleGroup();

        RadioButton connectBtn = new RadioButton("Connecter Formes");
        RadioButton selectBtn = new RadioButton("Selectionner pour Chemin");

        connectBtn.setToggleGroup(connectionGroup);
        selectBtn.setToggleGroup(connectionGroup);

        connectBtn.setOnAction(e -> {
            currentTool = "CONNECT";
            updateStatus("Mode Connexion - Cliquez sur deux formes pour les connecter");
        });
        selectBtn.setOnAction(e -> {
            currentTool = "SELECT";
            updateStatus("Mode Sélection - Cliquez sur forme de départ puis d'arrivée");
        });

        connectionTools.getChildren().addAll(connectBtn, selectBtn);

        // Algorithmes et calculs
        HBox algoTools = new HBox(10);
        Label algoLabel = new Label("Algorithme:");
        algoLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> algoCombo = new ComboBox<>();
        algoCombo.getItems().addAll("Dijkstra", "Bellman-Ford");
        algoCombo.setValue("Dijkstra");
        algoCombo.setOnAction(e -> {
            String algo = algoCombo.getValue();
            if ("Dijkstra".equals(algo)) {
                pathStrategy = new DijkstraStrategy();
            } else {
                pathStrategy = new BellmanFordStrategy();
            }
            LoggerSingleton.getInstance().log("Algorithme changé: " + algo);
        });

        Button calculateBtn = new Button("Calculer Plus Court Chemin");
        Button clearPathBtn = new Button("Effacer Chemin");

        calculateBtn.setOnAction(e -> calculateShortestPath());
        clearPathBtn.setOnAction(e -> clearPath());

        algoTools.getChildren().addAll(algoLabel, algoCombo, calculateBtn, clearPathBtn);

        section.getChildren().addAll(sectionTitle, connectionTools, algoTools);
        return section;
    }

    private VBox createActionsSection() {
        VBox section = new VBox(10);

        Label sectionTitle = new Label("3. Actions & Configuration");
        sectionTitle.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #dc3545;");

        // Fichiers
        HBox fileActions = new HBox(10);
        Button saveBtn = new Button("Sauvegarder");
        Button loadBtn = new Button("Ouvrir");
        Button clearBtn = new Button("Effacer Tout");

        saveBtn.setOnAction(e -> saveDrawing());
        loadBtn.setOnAction(e -> loadDrawing());
        clearBtn.setOnAction(e -> clearAll());

        fileActions.getChildren().addAll(saveBtn, loadBtn, clearBtn);

        // Journalisation
        HBox logActions = new HBox(10);
        Label logLabel = new Label("Journalisation:");
        logLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> logStrategy = new ComboBox<>();
        logStrategy.getItems().addAll("Console", "Fichier", "Base de données");
        logStrategy.setValue("Console");
        logStrategy.setOnAction(e -> changeLogStrategy(logStrategy.getValue()));

        logActions.getChildren().addAll(logLabel, logStrategy);

        section.getChildren().addAll(sectionTitle, fileActions, logActions);
        return section;
    }

    private void handleCanvasClick(javafx.scene.input.MouseEvent e) {
        double x = e.getX();
        double y = e.getY();

        switch (currentTool) {
            case "CIRCLE":
            case "RECTANGLE":
            case "LINE":
                handleShapeCreation(x, y);
                break;
            case "CONNECT":
                handleConnectionCreation(x, y);
                break;
            case "SELECT":
                selectShapeForPath(x, y);
                break;
        }

        redrawCanvas();
    }

    private void handleShapeCreation(double x, double y) {
        try {
            Shape shape;

            switch (currentTool) {
                case "CIRCLE":
                    shape = ShapeFactory.createShape("CIRCLE", x - 25, y - 25, selectedColor);
                    break;
                case "RECTANGLE":
                    shape = ShapeFactory.createShapeWithDimensions("RECTANGLE", x - 40, y - 30, 80, 60, selectedColor);
                    break;
                case "LINE":
                    shape = ShapeFactory.createLine(x - 50, y, x + 50, y, selectedColor);
                    break;
                default:
                    return;
            }

            drawingBoard.addShape(shape);
            LoggerSingleton.getInstance().log("Nœud-forme créé: " + currentTool +
                    " [" + shape.getNodeId() + "] à (" + String.format("%.0f", x) + ", " + String.format("%.0f", y)
                    + ")");
            updateStatus("Nœud " + currentTool + " [" + shape.getNodeId() + "] créé - " +
                    drawingBoard.getShapes().size() + " nœuds au total");

        } catch (Exception ex) {
            LoggerSingleton.getInstance().log("Erreur lors de la création de nœud-forme: " + ex.getMessage());
            updateStatus("Erreur lors de la création de nœud-forme");
        }
    }

    private void handleConnectionCreation(double x, double y) {
        Shape clickedShape = findShapeAt(x, y);

        if (clickedShape != null) {
            if (selectedShape1 == null) {
                selectedShape1 = clickedShape;
                selectedShape1.setSelected(true);
                updateStatus("Premier nœud sélectionné: [" + selectedShape1.getNodeId() +
                        "] - Cliquez sur un autre nœud pour les connecter");
            } else if (!selectedShape1.equals(clickedShape)) {
                // Créer la connexion
                ShapeConnection connection = new ShapeConnection(selectedShape1, clickedShape);
                connections.add(connection);

                updateStatus("Connexion créée: [" + selectedShape1.getNodeId() + "] ↔ [" +
                        clickedShape.getNodeId() + "] (distance: " + String.format("%.1f", connection.getWeight())
                        + ")");
                LoggerSingleton.getInstance().log("Connexion créée entre " + selectedShape1.getNodeId() +
                        " et " + clickedShape.getNodeId());

                selectedShape1.setSelected(false);
                selectedShape1 = null;
            } else {
                updateStatus("Vous ne pouvez pas connecter un nœud à lui-même");
            }
        } else {
            updateStatus("Cliquez sur une forme pour la connecter");
        }
    }

    private void selectShapeForPath(double x, double y) {
        Shape clickedShape = findShapeAt(x, y);

        if (clickedShape != null) {
            if (startPathShape == null) {
                startPathShape = clickedShape;
                startPathShape.setSelected(true);
                updateStatus("Nœud de départ: [" + startPathShape.getNodeId() +
                        "] - Sélectionnez le nœud d'arrivée");
            } else if (endPathShape == null && !clickedShape.equals(startPathShape)) {
                endPathShape = clickedShape;
                endPathShape.setSelected(true);
                updateStatus("Nœud d'arrivée: [" + endPathShape.getNodeId() +
                        "] - Cliquez sur 'Calculer Plus Court Chemin'");
            } else {
                clearPathSelections();
                startPathShape = clickedShape;
                startPathShape.setSelected(true);
                updateStatus("Nouveau départ: [" + startPathShape.getNodeId() + "]");
            }
        } else {
            updateStatus("Cliquez sur une forme pour la sélectionner");
        }
    }

    private void calculateShortestPath() {
        if (startPathShape == null || endPathShape == null) {
            showAlert("Veuillez sélectionner un nœud de départ et un nœud d'arrivée");
            return;
        }

        // Utiliser l'algorithme de Dijkstra adapté pour les formes
        List<Shape> path = findShortestPathBetweenShapes(startPathShape, endPathShape);

        if (path.isEmpty()) {
            showAlert("Aucun chemin trouvé entre [" + startPathShape.getNodeId() +
                    "] et [" + endPathShape.getNodeId() + "]");
        } else {
            highlightPath(path);
            StringBuilder pathStr = new StringBuilder();
            for (int i = 0; i < path.size(); i++) {
                pathStr.append("[").append(path.get(i).getNodeId()).append("]");
                if (i < path.size() - 1)
                    pathStr.append(" → ");
            }
            LoggerSingleton.getInstance().log("Plus court chemin: " + pathStr.toString());
            showAlert("Plus court chemin trouvé: " + pathStr.toString());
            updateStatus("Chemin calculé: " + pathStr.toString());
        }
    }

    private List<Shape> findShortestPathBetweenShapes(Shape start, Shape end) {
        // Implémentation simplifiée de Dijkstra pour les formes
        Map<Shape, Double> distances = new HashMap<>();
        Map<Shape, Shape> previous = new HashMap<>();
        List<Shape> unvisited = new ArrayList<>(drawingBoard.getShapes());

        // Initialiser les distances
        for (Shape shape : drawingBoard.getShapes()) {
            distances.put(shape, Double.MAX_VALUE);
        }
        distances.put(start, 0.0);

        while (!unvisited.isEmpty()) {
            // Trouver le nœud non visité avec la plus petite distance
            Shape current = unvisited.stream()
                    .min((a, b) -> Double.compare(distances.get(a), distances.get(b)))
                    .orElse(null);

            if (current == null || distances.get(current) == Double.MAX_VALUE) {
                break;
            }

            unvisited.remove(current);

            if (current.equals(end)) {
                break;
            }

            // Examiner les voisins (formes connectées)
            for (ShapeConnection connection : connections) {
                Shape neighbor = null;
                if (connection.getFromShape().equals(current)) {
                    neighbor = connection.getToShape();
                } else if (connection.getToShape().equals(current)) {
                    neighbor = connection.getFromShape();
                }

                if (neighbor != null && unvisited.contains(neighbor)) {
                    double alt = distances.get(current) + connection.getWeight();
                    if (alt < distances.get(neighbor)) {
                        distances.put(neighbor, alt);
                        previous.put(neighbor, current);
                    }
                }
            }
        }

        // Reconstruire le chemin
        List<Shape> path = new ArrayList<>();
        Shape current = end;
        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }

        return path.isEmpty() || !path.get(0).equals(start) ? new ArrayList<>() : path;
    }

    private void highlightPath(List<Shape> path) {
        // Effacer les anciens highlights
        clearPath();

        // Mettre en évidence les formes du chemin
        for (Shape shape : path) {
            shape.setHighlighted(true);
        }

        // Mettre en évidence les connexions du chemin
        for (int i = 0; i < path.size() - 1; i++) {
            Shape from = path.get(i);
            Shape to = path.get(i + 1);

            for (ShapeConnection connection : connections) {
                if (connection.connects(from, to)) {
                    connection.setHighlighted(true);
                    break;
                }
            }
        }
    }

    private Shape findShapeAt(double x, double y) {
        for (Shape shape : drawingBoard.getShapes()) {
            if (shape.contains(x, y)) {
                return shape;
            }
        }
        return null;
    }

    private void changeLogStrategy(String strategy) {
        switch (strategy) {
            case "Console":
                LoggerSingleton.getInstance().setStrategy(new ConsoleLogger());
                break;
            case "Fichier":
                LoggerSingleton.getInstance().setStrategy(new FileLogger());
                break;
            case "Base de données":
                LoggerSingleton.getInstance().setStrategy(new DBLogger());
                break;
        }
        LoggerSingleton.getInstance().log("Stratégie de journalisation changée: " + strategy);
    }

    private void saveDrawing() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Sauvegarder le graphe de formes");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Fichiers de dessin", "*.drawing"));

        File file = fileChooser.showSaveDialog(canvas.getScene().getWindow());
        if (file != null) {
            ShapePersistenceAdapter adapter = new ShapePersistenceAdapter();
            adapter.setStrategy(new FilePersistenceStrategy());
            adapter.saveShapes(drawingBoard.getShapes(), file.getAbsolutePath());
            LoggerSingleton.getInstance().log("Graphe de formes sauvegardé: " + file.getName());
            updateStatus("Graphe sauvegardé: " + file.getName());
        }
    }

    private void loadDrawing() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Ouvrir un graphe de formes");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Fichiers de dessin", "*.drawing"));

        File file = fileChooser.showOpenDialog(canvas.getScene().getWindow());
        if (file != null) {
            ShapePersistenceAdapter adapter = new ShapePersistenceAdapter();
            adapter.setStrategy(new FilePersistenceStrategy());
            List<Shape> shapes = adapter.loadShapes(file.getAbsolutePath());
            drawingBoard.clearShapes();
            connections.clear();
            for (Shape shape : shapes) {
                drawingBoard.addShape(shape);
            }
            LoggerSingleton.getInstance().log("Graphe de formes ouvert: " + file.getName());
            updateStatus("Graphe ouvert: " + file.getName());
            redrawCanvas();
        }
    }

    private void clearAll() {
        drawingBoard.clearShapes();
        connections.clear();
        clearPathSelections();
        clearSelections();
        clearCanvas();
        LoggerSingleton.getInstance().log("Graphe de formes effacé");
        updateStatus("Tout effacé - Créez de nouvelles formes-nœuds");
    }

    private void clearPath() {
        // Effacer les highlights des formes
        for (Shape shape : drawingBoard.getShapes()) {
            shape.setHighlighted(false);
        }

        // Effacer les highlights des connexions
        for (ShapeConnection connection : connections) {
            connection.setHighlighted(false);
        }

        redrawCanvas();
        LoggerSingleton.getInstance().log("Chemin effacé");
        updateStatus("Chemin effacé");
    }

    private void clearPathSelections() {
        if (startPathShape != null) {
            startPathShape.setSelected(false);
            startPathShape = null;
        }
        if (endPathShape != null) {
            endPathShape.setSelected(false);
            endPathShape = null;
        }
    }

    private void clearSelections() {
        if (selectedShape1 != null) {
            selectedShape1.setSelected(false);
            selectedShape1 = null;
        }
        if (selectedShape2 != null) {
            selectedShape2.setSelected(false);
            selectedShape2 = null;
        }
    }

    private void clearCanvas() {
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        gc.setStroke(Color.BLACK);
        gc.strokeRect(0, 0, canvas.getWidth(), canvas.getHeight());
    }

    private void redrawCanvas() {
        clearCanvas();

        // Dessiner toutes les connexions d'abord (en arrière-plan)
        for (ShapeConnection connection : connections) {
            connection.draw(gc);
        }

        // Dessiner toutes les formes-nœuds par-dessus
        for (Shape shape : drawingBoard.getShapes()) {
            // Dessiner la forme normale
            shape.draw(gc);

            // Ajouter des effets visuels si sélectionnée ou mise en évidence
            if (shape.isSelected()) {
                gc.setStroke(Color.ORANGE);
                gc.setLineWidth(3);
                gc.strokeOval(shape.getCenterX() - 30, shape.getCenterY() - 30, 60, 60);
            }

            if (shape.isHighlighted()) {
                gc.setStroke(Color.RED);
                gc.setLineWidth(2);
                gc.strokeOval(shape.getCenterX() - 35, shape.getCenterY() - 35, 70, 70);
            }

            // Afficher l'ID du nœud
            gc.setFill(Color.BLACK);
            gc.fillText(shape.getNodeId(), shape.getCenterX() - 10, shape.getCenterY() - 40);
        }

        // Remettre les paramètres par défaut
        gc.setLineWidth(1);
        gc.setStroke(Color.BLACK);
    }

    private void updateStatus(String message) {
        statusLabel.setText(message);
    }

    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Information");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
