package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Strategy concrète pour dessiner des rectangles
 * Version corrigée qui utilise le polymorphisme
 */
public class RectangleDrawingStrategy implements DrawingStrategy {

    @Override
    public void draw(GraphicsContext gc, Shape shape) {
        // Couleur de remplissage
        gc.setFill(shape.getColor());
        gc.fillRect(shape.getX(), shape.getY(), shape.getWidth(), shape.getHeight());

        // Contour
        gc.setStroke(Color.BLACK);
        gc.setLineWidth(2);
        gc.strokeRect(shape.getX(), shape.getY(), shape.getWidth(), shape.getHeight());
    }
}
