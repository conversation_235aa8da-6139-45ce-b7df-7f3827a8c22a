# 🎨 Guide d'Utilisation - Application de Dessin Unifiée

## 📋 Vue d'Ensemble

L'**Application de Dessin Unifiée** combine maintenant toutes les fonctionnalités en une seule interface :
- **Dessin de formes géométriques** (cercles, rectangles, lignes)
- **Analyse de graphes** avec calcul du plus court chemin
- **Journalisation configurable** (console, fichier, base de données)
- **Sauvegarde/Ouverture** des dessins

## 🚀 Lancement de l'Application

### Option 1: <PERSON><PERSON><PERSON> (Recommandé)
```bash
run_unified_app.bat
```

### Option 2: Ligne de commande
```bash
cd "c:\Users\<USER>\eclipse-workspace\designPattern_MiniProjet"
java --module-path "C:\Program Files\Java\javafx-sdk-21.0.1\lib" --add-modules javafx.controls,javafx.fxml -cp "bin" designPattern_MiniProjet.UnifiedDrawingApplication
```

## 🎯 Fonctionnalités Principales

### 1. **Mode Dessin de Formes** 🎨

#### Sélection des Formes:
- **⭕ Cercle** : Cliquez pour créer un cercle
- **⬛ Rectangle** : Cliquez pour créer un rectangle  
- **➖ Ligne** : Cliquez pour créer une ligne

#### Sélection des Couleurs:
- **Sélecteur de couleur** personnalisé
- **Couleurs rapides** : 🔴 Rouge, 🔵 Bleu, 🟢 Vert, 🟡 Jaune

#### Utilisation:
1. Sélectionnez une forme dans la palette
2. Choisissez une couleur
3. Cliquez sur la zone de dessin pour créer la forme

### 2. **Mode Analyse de Graphes** 🔗

#### Modes de Création:
- **➕ Créer nœuds** : Cliquez pour ajouter des nœuds
- **🔗 Créer arêtes** : Cliquez sur deux nœuds pour les connecter
- **👆 Sélectionner** : Sélectionnez les nœuds de départ et d'arrivée

#### Algorithmes Disponibles:
- **Dijkstra** : Algorithme classique pour graphes pondérés positifs
- **Bellman-Ford** : Algorithme pour graphes avec poids négatifs

#### Workflow:
1. Créez des nœuds en cliquant sur la zone
2. Connectez-les avec des arêtes
3. Sélectionnez les nœuds de départ et d'arrivée
4. Cliquez "🧮 Calculer chemin" pour voir le plus court chemin

### 3. **Journalisation** 📝

Configurez le type de journalisation depuis la barre d'outils :
- **Console** : Messages dans la console
- **Fichier** : Enregistrement dans un fichier de log
- **Base de données** : Stockage en base de données

### 4. **Gestion des Fichiers** 💾

- **💾 Sauvegarder** : Sauvegarde le dessin actuel
- **📂 Ouvrir** : Charge un dessin existant
- **🗑️ Effacer tout** : Efface la zone de dessin

## 🏗️ Architecture et Design Patterns

### Patterns Implémentés:

1. **Singleton Pattern**:
   - `LoggerSingleton` : Instance unique du logger
   - `DrawingBoard` : Instance unique du tableau de dessin

2. **Strategy Pattern**:
   - `LoggerStrategy` : Différentes stratégies de journalisation
   - `DrawingStrategy` : Stratégies de dessin pour chaque forme
   - `ShortestPathStrategy` : Algorithmes de plus court chemin

3. **Observer Pattern**:
   - `DrawingObserver` : Notifications des changements de dessin

4. **Factory Pattern**:
   - `ShapeFactory` : Création des formes géométriques

5. **Adapter Pattern**:
   - `ShapePersistenceAdapter` : Adaptation pour la persistance

## 🔧 Fonctionnalités Avancées

### Interface Unifiée:
- **Onglets** pour basculer entre modes
- **Barre d'état** avec informations en temps réel
- **Interface intuitive** avec icônes et couleurs

### Journalisation Intelligente:
- Toutes les actions sont loggées automatiquement
- Changement de stratégie à la volée
- Messages détaillés pour le debugging

### Persistance:
- Sauvegarde des formes dans des fichiers
- Format personnalisé `.drawing`
- Rechargement avec reconstruction des objets

## 📊 Avantages de l'Unification

### Avant (2 applications séparées):
- ❌ Interface incohérente
- ❌ Code dupliqué pour les fonctionnalités communes
- ❌ Maintenance difficile
- ❌ Experience utilisateur fragmentée

### Maintenant (1 application unifiée):
- ✅ **Interface cohérente** et professionnelle
- ✅ **Code réutilisé** pour les fonctionnalités communes
- ✅ **Maintenance simplifiée** avec une seule base de code
- ✅ **Expérience utilisateur fluide** avec basculement facile entre modes
- ✅ **Architecture claire** avec tous les patterns bien intégrés

## 🎯 Cas d'Usage Complets

### Scénario 1: Dessin Artistique
1. Lancez l'application
2. Restez sur l'onglet "🎨 Dessin de Formes"
3. Créez votre composition avec différentes formes et couleurs
4. Sauvegardez votre œuvre

### Scénario 2: Analyse de Réseau
1. Basculez sur l'onglet "🔗 Analyse de Graphes"
2. Créez un réseau de nœuds et d'arêtes
3. Sélectionnez deux points d'intérêt
4. Analysez le plus court chemin avec différents algorithmes

### Scénario 3: Étude Comparative
1. Créez le même graphe
2. Testez Dijkstra vs Bellman-Ford
3. Observez les différences dans les logs
4. Sauvegardez les résultats

## 🔍 Débogage et Logs

Toutes les actions sont automatiquement loggées :
- Création de formes
- Sélections de nœuds
- Calculs de chemins
- Changements de stratégies
- Opérations de fichiers

**Conseil** : Utilisez la journalisation fichier pour analyser les performances et le comportement de l'application.

---

Cette application unifiée représente maintenant la **solution complète** selon vos spécifications, intégrant tous les design patterns requis dans une interface moderne et cohérente ! 🚀
