package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe concrète Circle
 * Utilise le pattern Strategy pour le dessin
 */
public class Circle extends Shape {
    private double radius;
    
    public Circle(double centerX, double centerY, double radius, Color color) {
        super(centerX - radius, centerY - radius, radius * 2, radius * 2, color);
        this.radius = radius;
        this.drawingStrategy = new CircleDrawingStrategy();
    }
    
    public Circle(double centerX, double centerY, double radius) {
        this(centerX, centerY, radius, Color.RED);
    }
    
    public Circle(double centerX, double centerY) {
        this(centerX, centerY, 40); // Rayon par défaut
    }
    
    public double getRadius() {
        return radius;
    }
    
    public void setRadius(double radius) {
        this.radius = radius;
        this.width = radius * 2;
        this.height = radius * 2;
    }
    
    @Override
    public boolean contains(double pointX, double pointY) {
        double centerX = x + radius;
        double centerY = y + radius;
        return Math.pow(pointX - centerX, 2) + Math.pow(pointY - centerY, 2) <= Math.pow(radius, 2);
    }
    
    @Override
    public String serialize() {
        return String.format("CIRCLE,%.2f,%.2f,%.2f,%s", 
            x + radius, y + radius, radius, color.toString());
    }
    
    @Override
    public String getType() {
        return "Circle";
    }
    
    // Factory method pour créer depuis une chaîne sérialisée
    public static Circle deserialize(String data) {
        String[] parts = data.split(",");
        if (parts.length >= 4 && "CIRCLE".equals(parts[0])) {
            double centerX = Double.parseDouble(parts[1]);
            double centerY = Double.parseDouble(parts[2]);
            double radius = Double.parseDouble(parts[3]);
            Color color = parts.length > 4 ? Color.valueOf(parts[4]) : Color.RED;
            return new Circle(centerX, centerY, radius, color);
        }
        throw new IllegalArgumentException("Invalid circle data: " + data);
    }
}
